<template>
  <div id="app" class="h-screen w-screen overflow-hidden bg-background text-foreground">
    <RouterView />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'

/**
 * {{APP_NAME}} 根组件
 */

onMounted(() => {
  window.logger?.info('{{APP_NAME}} 组件已挂载')
})
</script>

<style scoped>
#app {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
