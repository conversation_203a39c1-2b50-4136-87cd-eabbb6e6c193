<template>
  <div class="h-full flex flex-col">
    <!-- 新建工作流按钮 -->
    <div class="px-4 py-3 border-b border-border">
      <Button class="w-full" @click="handleCreateWorkflow">
        <MattIcon name="Plus" class="mr-2 h-4 w-4" />
        新建工作流
      </Button>
    </div>

    <!-- 文件夹区域 -->
    <div class="flex-1 flex flex-col px-4 py-3 overflow-hidden">
      <!-- 文件夹标题和新建按钮 -->
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-medium text-muted-foreground">文件夹</h3>
        <Button
          variant="ghost"
          size="sm"
          class="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
          @click="handleCreateFolder"
        >
          <MattIcon name="Plus" class="h-4 w-4" />
        </Button>
      </div>

      <!-- 文件夹列表 -->
      <div class="flex-1 overflow-y-auto space-y-1">
        <div
          v-for="folder in foldersWithStats"
          :key="folder.id"
          class="group flex items-center justify-between rounded-md px-2 py-2 text-sm cursor-pointer transition-colors hover:bg-accent"
          :class="{
            'bg-accent text-accent-foreground': currentFolderId === folder.id,
          }"
          @click="handleSelectFolder(folder.id)"
        >
          <div class="flex items-center min-w-0 flex-1">
            <MattIcon
              :name="folder.icon || 'Folder'"
              class="mr-2 h-4 w-4 flex-shrink-0"
              :style="{ color: folder.color }"
            />
            <span class="truncate">{{ folder.name }}</span>
            <span class="ml-2 text-xs text-muted-foreground"> ({{ folder.workflowCount }}) </span>
          </div>

          <!-- 操作按钮 -->
          <div
            v-if="!folder.isSystem"
            class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="ghost" size="sm" class="h-6 w-6 p-0" @click.stop>
                  <MattIcon name="MoreHorizontal" class="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" class="border-none">
                <DropdownMenuItem @click="handleEditFolder(folder)">
                  <MattIcon name="Edit2" class="mr-2 h-4 w-4" />
                  重命名
                </DropdownMenuItem>
                <DropdownMenuItem @click="handleDuplicateFolder(folder.id)">
                  <MattIcon name="Copy" class="mr-2 h-4 w-4" />
                  复制
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  class="text-destructive focus:text-destructive"
                  @click="handleDeleteFolder(folder)"
                >
                  <MattIcon name="Trash2" class="mr-2 h-4 w-4 text-destructive" />
                  删除
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件夹对话框 -->
    <Dialog v-model:open="folderDialogOpen">
      <DialogContent class="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {{ folderDialogMode === 'create' ? '新建文件夹' : '编辑文件夹' }}
          </DialogTitle>
        </DialogHeader>
        <div class="space-y-4 py-4">
          <div class="space-y-2">
            <Label for="folder-name">名称</Label>
            <Input
              id="folder-name"
              v-model="folderForm.name"
              placeholder="请输入名称"
              @keyup.enter="handleConfirmFolder"
            />
          </div>
          <div class="space-y-2">
            <Label for="folder-description">描述</Label>
            <Textarea
              id="folder-description"
              v-model="folderForm.description"
              placeholder="请输入描述"
              rows="3"
              class="resize-none"
            />
            <div class="text-xs text-muted-foreground text-right">
              {{ folderForm.description.length }}/350
            </div>
          </div>
        </div>
        <DialogFooter class="gap-2">
          <Button variant="outline" @click="folderDialogOpen = false" class="flex-1"> 取消 </Button>
          <Button
            @click="handleConfirmFolder"
            :disabled="!folderForm.name.trim()"
            class="flex-1 bg-black text-white hover:bg-black/90"
          >
            {{ folderDialogMode === 'create' ? '添加' : '保存' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 删除确认对话框 -->
    <AlertDialog v-model:open="deleteDialogOpen">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除文件夹 "{{ folderToDelete?.name }}" 吗？
            <br />
            此操作将删除文件夹及其中的所有工作流（{{ folderToDelete?.workflowCount || 0 }}
            个），且无法撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="deleteDialogOpen = false"> 取消 </AlertDialogCancel>
          <AlertDialogAction
            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            @click="confirmDeleteFolder"
          >
            删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useFolderStore, useWorkflowStore, useNavbarStore } from '@/store'
import { storeToRefs } from 'pinia'
import { toast } from 'vue-sonner'

// Store
const folderStore = useFolderStore()
const workflowStore = useWorkflowStore()
const navbarStore = useNavbarStore()
const { currentFolderId } = storeToRefs(folderStore)

// 计算属性
const foldersWithStats = computed(() => folderStore.getAllFoldersWithStats())

// 对话框状态
const folderDialogOpen = ref(false)
const folderDialogMode = ref<'create' | 'edit'>('create')
const deleteDialogOpen = ref(false)

// 表单数据
const folderForm = ref({
  id: '',
  name: '',
  description: '',
})

const folderToDelete = ref<any>(null)

// 事件定义
const emit = defineEmits<{
  createWorkflow: []
}>()

// 方法
const handleCreateWorkflow = () => {
  emit('createWorkflow')
}

const handleSelectFolder = (folderId: string) => {
  folderStore.setCurrentFolder(folderId)
}

const handleCreateFolder = () => {
  folderDialogMode.value = 'create'
  folderForm.value = {
    id: '',
    name: '',
    description: '',
  }
  folderDialogOpen.value = true
}

const handleEditFolder = (folder: any) => {
  folderDialogMode.value = 'edit'
  folderForm.value = {
    id: folder.id,
    name: folder.name,
    description: folder.description || '',
  }
  folderDialogOpen.value = true
}

const handleDuplicateFolder = (folderId: string) => {
  const newFolder = folderStore.duplicateFolder(folderId)
  if (newFolder) {
    toast.success('文件夹复制成功', {
      description: `已创建文件夹 "${newFolder.name}"`,
    })
  }
}

const handleDeleteFolder = (folder: any) => {
  folderToDelete.value = folder
  deleteDialogOpen.value = true
}

const handleConfirmFolder = () => {
  const name = folderForm.value.name.trim()
  if (!name) {
    toast.error('请输入文件夹名称')
    return
  }

  if (folderDialogMode.value === 'create') {
    const newFolder = folderStore.createFolder({
      name,
      description: folderForm.value.description,
    })
    toast.success('文件夹创建成功', {
      description: `已创建文件夹 "${newFolder.name}"`,
    })
  } else {
    const success = folderStore.updateFolder({
      id: folderForm.value.id,
      name,
      description: folderForm.value.description,
    })
    if (success) {
      toast.success('文件夹更新成功')
    } else {
      toast.error('文件夹更新失败')
    }
  }

  folderDialogOpen.value = false
}

const confirmDeleteFolder = () => {
  if (folderToDelete.value) {
    const folderId = folderToDelete.value.id
    const folderName = folderToDelete.value.name
    const workflowCount = folderToDelete.value.workflowCount

    // 获取该文件夹下的所有工作流ID，用于删除对应的页签
    const workflowsInFolder = workflowStore.getWorkflowsByFolder(folderId)
    const workflowIds = workflowsInFolder.map((wf) => wf.id)

    const success = folderStore.deleteFolder(folderId)
    if (success) {
      // 同步删除该文件夹下所有工作流的页签
      workflowIds.forEach((workflowId) => {
        navbarStore.removeWorkflowTab(workflowId)
      })

      toast.success('文件夹删除成功', {
        description: `已删除文件夹 "${folderName}" 及其中的 ${workflowCount} 个工作流`,
      })
    } else {
      toast.error('文件夹删除失败')
    }
  }
  deleteDialogOpen.value = false
  folderToDelete.value = null
}
</script>
