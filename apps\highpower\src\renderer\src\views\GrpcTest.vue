<template>
  <div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">gRPC 客户端测试</h1>

    <!-- 连接状态 -->
    <div class="mb-6 p-4 border rounded-lg">
      <h2 class="text-lg font-semibold mb-3">连接状态</h2>
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-2">
          <div :class="['w-3 h-3 rounded-full', isConnected ? 'bg-green-500' : 'bg-red-500']"></div>
          <span>{{ isConnected ? '已连接' : '未连接' }}</span>
        </div>

        <button
          @click="handleConnect"
          :disabled="isConnecting"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {{ isConnecting ? '连接中...' : '连接' }}
        </button>

        <button
          @click="handleDisconnect"
          :disabled="!isConnected"
          class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
        >
          断开连接
        </button>
      </div>

      <div v-if="error" class="mt-2 text-red-600">错误: {{ error }}</div>
    </div>

    <!-- 连接配置 -->
    <div class="mb-6 p-4 border rounded-lg">
      <h2 class="text-lg font-semibold mb-3">连接配置</h2>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium mb-1">服务器地址</label>
          <input
            v-model="config.host"
            type="text"
            class="w-full px-3 py-2 border rounded-md"
            placeholder="localhost"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">端口</label>
          <input
            v-model.number="config.port"
            type="number"
            class="w-full px-3 py-2 border rounded-md"
            placeholder="50051"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">用户ID</label>
          <input
            v-model="config.userId"
            type="text"
            class="w-full px-3 py-2 border rounded-md"
            placeholder="test-user"
          />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">Token</label>
          <input
            v-model="config.token"
            type="text"
            class="w-full px-3 py-2 border rounded-md"
            placeholder="test-token"
          />
        </div>
      </div>
    </div>

    <!-- 测试操作 -->
    <div class="mb-6 p-4 border rounded-lg">
      <h2 class="text-lg font-semibold mb-3">测试操作</h2>
      <div class="flex flex-wrap gap-2">
        <button
          @click="handlePing"
          :disabled="!isConnected"
          class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          Ping 测试
        </button>

        <button
          @click="handleRegister"
          :disabled="!isConnected"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          注册服务器
        </button>

        <button
          @click="handleDefaultService"
          :disabled="!isConnected"
          class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
        >
          默认服务调用
        </button>

        <button
          @click="handleSubmitService"
          :disabled="!isConnected"
          class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
        >
          提交服务调用
        </button>

        <button
          @click="handleDatabase"
          :disabled="!isConnected"
          class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 disabled:opacity-50"
        >
          数据库操作
        </button>
      </div>
    </div>

    <!-- 结果显示 -->
    <div class="p-4 border rounded-lg">
      <h2 class="text-lg font-semibold mb-3">操作结果</h2>
      <div class="bg-gray-100 p-4 rounded-md max-h-96 overflow-y-auto">
        <pre class="text-sm">{{ results }}</pre>
      </div>
      <button
        @click="clearResults"
        class="mt-2 px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
      >
        清空结果
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 直接使用 IPC 调用主进程中的 gRPC 客户端
const isConnected = ref(false)
const isInitialized = ref(false)
const error = ref<string | null>(null)

// 配置
const config = ref({
  host: 'localhost',
  port: 50051,
  userId: 'highpower-user',
  token: 'highpower-token',
})

// 结果显示
const results = ref<string>('')

const addResult = (operation: string, result: any) => {
  const timestamp = new Date().toLocaleTimeString()
  const resultText = `[${timestamp}] ${operation}:\n${JSON.stringify(result, null, 2)}\n\n`
  results.value += resultText
}

const clearResults = () => {
  results.value = ''
}

// 获取 gRPC 状态
const getGrpcStatus = async () => {
  try {
    const result = await window.electronAPI.invoke('grpc:get-status')
    isInitialized.value = result.initialized
    isConnected.value = result.connected
    return result
  } catch (err) {
    error.value = err instanceof Error ? err.message : '获取状态失败'
    return null
  }
}

// 连接操作
const handleConnect = async () => {
  try {
    const result = await window.electronAPI.invoke('grpc:init')
    if (result.success) {
      isInitialized.value = true
      isConnected.value = result.connected
      addResult('初始化 gRPC', result)
    } else {
      error.value = result.error || '初始化失败'
      addResult('初始化 gRPC 失败', result)
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '初始化失败'
    addResult('初始化 gRPC 失败', { error: error.value })
  }
}

const handleDisconnect = () => {
  isConnected.value = false
  isInitialized.value = false
  addResult('断开连接', { success: true })
}

// 测试操作
const handlePing = async () => {
  try {
    const result = await window.electronAPI.invoke('grpc:ping')
    addResult('Ping 测试', result)
  } catch (err) {
    addResult('Ping 失败', { error: err instanceof Error ? err.message : '未知错误' })
  }
}

const handleRegister = async () => {
  try {
    const params = {
      server_name: 'highpower-test',
      url: 'http://localhost:3000',
      region: 'local',
      version: '1.0.0',
      access_level: 1,
      protocol_type: 'http',
      is_force_to_register: false,
      service_name_list: ['compute-service'],
      service_version_list: ['1.0.0'],
      service_access_level_list: [1],
      server_type: 'compute',
    }
    const result = await window.electronAPI.invoke('grpc:call', 'register', params)
    addResult('注册服务器', result)
  } catch (err) {
    addResult('注册服务器失败', { error: err instanceof Error ? err.message : '未知错误' })
  }
}

const handleDefaultService = async () => {
  try {
    const params = {
      service_name: 'test-service',
      method_name: 'get_info',
      task_id: 'test-task-' + Date.now(),
      server_id: 'test-server',
      key_type_pairs: {},
      key_value_pairs: { test: 'data' },
    }
    const result = await window.electronAPI.invoke('grpc:call', 'defaultService', params)
    addResult('默认服务调用', result)
  } catch (err) {
    addResult('默认服务调用失败', { error: err instanceof Error ? err.message : '未知错误' })
  }
}

const handleSubmitService = async () => {
  try {
    const params = {
      service_name: 'compute-service',
      method_name: 'submit_task',
      task_id: 'compute-task-' + Date.now(),
      server_id: 'compute-server',
      key_type_pairs: { task_type: 'string' },
      key_value_pairs: { task_type: 'matrix_multiply', data: [1, 2, 3, 4] },
    }
    const result = await window.electronAPI.invoke('grpc:call', 'submitService', params)
    addResult('提交服务调用', result)
  } catch (err) {
    addResult('提交服务调用失败', { error: err instanceof Error ? err.message : '未知错误' })
  }
}

const handleDatabase = async () => {
  try {
    const params = {
      operation: 'select',
      table_name: 'test_table',
      conditions: { id: 1 },
      options: { limit: 10 },
    }
    const result = await window.electronAPI.invoke('grpc:call', 'database', params)
    addResult('数据库操作', result)
  } catch (err) {
    addResult('数据库操作失败', { error: err instanceof Error ? err.message : '未知错误' })
  }
}

// 页面加载时获取状态
getGrpcStatus()
</script>
