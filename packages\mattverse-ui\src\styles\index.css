/* UI Components 样式 - shadcn-vue + Tailwind CSS v4 */
@import 'tailwindcss';
@import '@mattverse/shared/styles/theme.css';

/* ===== 组件样式增强 ===== */
@layer components {
  /* 确保侧边栏组件样式正确应用 */
  .sidebar {
    background-color: hsl(var(--sidebar));
    color: hsl(var(--sidebar-foreground));
    border-color: hsl(var(--sidebar-border));
  }

  .sidebar-primary {
    background-color: hsl(var(--sidebar-primary));
    color: hsl(var(--sidebar-primary-foreground));
  }

  .sidebar-accent {
    background-color: hsl(var(--sidebar-accent));
    color: hsl(var(--sidebar-accent-foreground));
  }
}
