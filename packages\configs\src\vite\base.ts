/**
 * Vite 基础配置工厂
 */
import path from 'node:path'
import vue from '@vitejs/plugin-vue'
import { defineConfig, type UserConfig } from 'vite'

export interface ViteConfigOptions {
  packageName?: string
  customAlias?: Record<string, string>
  customPlugins?: any[]
  buildOptions?: {
    minify?: boolean
    sourcemap?: boolean
    target?: string
  }
}

/**
 * 创建基础 Vite 配置
 */
export const createBaseViteConfig = (options: ViteConfigOptions = {}): UserConfig => {
  const { packageName = '', customAlias = {}, customPlugins = [], buildOptions = {} } = options

  // 基础别名配置
  const baseAlias = {
    '@': path.resolve(process.cwd(), './src'),
    ...customAlias,
  }

  return defineConfig({
    plugins: [vue(), ...customPlugins],
    resolve: { alias: baseAlias },
    css: {
      postcss: {
        plugins: [require('tailwindcss'), require('autoprefixer')],
      },
    },
    build: {
      outDir: 'dist',
      emptyOutDir: true,
      minify: buildOptions.minify ?? process.env.NODE_ENV === 'production',
      sourcemap: buildOptions.sourcemap ?? process.env.NODE_ENV !== 'production',
      lib: packageName
        ? {
            entry: path.resolve(process.cwd(), 'src/index.ts'),
            name: packageName,
            fileName: 'index',
          }
        : undefined,
    },
  })
}
