/**
 * 预加载进程 API 统一导出
 */
import { systemAPI } from './system'
import { appAPI } from './app'
import { middlewareAPI } from './middleware'
import { grpcAPI } from './grpc'

/**
 * 合并所有 API 模块
 */
export const customAPI = {
  // 系统信息
  ...systemAPI,
  
  // Mattverse 应用配置
  ...appAPI,

  // 中台配置相关 API
  middleware: middlewareAPI,

  // gRPC 相关 API
  grpc: grpcAPI,
}

/**
 * 导出各个模块的 API（如果需要单独使用）
 */
export {
  systemAPI,
  appAPI,
  middlewareAPI,
  grpcAPI,
}
