{"extends": "../../packages/configs/src/typescript/electron-app.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/renderer/src/*"], "@main/*": ["./src/main/*"], "@preload/*": ["./src/preload/*"], "@mattverse/shared": ["../../packages/shared/src"], "@mattverse/mattverse-ui": ["../../packages/mattverse-ui/src"], "@mattverse/mattverse-flow": ["../../packages/mattverse-flow/src"], "@mattverse/electron-core": ["../../packages/electron-core/src"], "@mattverse/configs": ["../../packages/configs/src"]}}, "include": ["src/**/*", "electron.vite.config.ts"], "exclude": ["node_modules", "dist", "out", "release", "**/*.test.*", "**/*.spec.*"]}