import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { nanoid } from 'nanoid'
import { useWorkflowStore } from './workflow'
import { createPersistConfig } from '@/store/plugins/persist-config'

/**
 * 文件夹接口
 */
export interface FolderItem {
  id: string
  name: string
  description?: string
  createTime: string
  updateTime: string
  color?: string
  icon?: string
  isSystem?: boolean // 系统文件夹（如"全部"）不可删除
}

/**
 * 文件夹创建参数
 */
export interface CreateFolderParams {
  name: string
  description?: string
  color?: string
  icon?: string
}

/**
 * 文件夹更新参数
 */
export interface UpdateFolderParams {
  id: string
  name?: string
  description?: string
  color?: string
  icon?: string
}

/**
 * 文件夹状态管理
 */
export const useFolderStore = defineStore(
  'folder',
  () => {
    // 状态
    const folders = ref<FolderItem[]>([
      {
        id: 'all',
        name: '全部',
        description: '显示所有工作流',
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString(),
        icon: 'FolderOpen',
        isSystem: true
      }
    ])
    const currentFolderId = ref<string>('all')

    // 计算属性
    const currentFolder = computed(() => 
      folders.value.find(folder => folder.id === currentFolderId.value)
    )

    const userFolders = computed(() => 
      folders.value.filter(folder => !folder.isSystem)
    )

    const folderCount = computed(() => folders.value.length)

    // 获取文件夹中的工作流数量
    const getFolderWorkflowCount = (folderId: string) => {
      const workflowStore = useWorkflowStore()
      if (folderId === 'all') {
        return workflowStore.workflows.length
      }
      return workflowStore.getWorkflowsByFolder(folderId).length
    }

    // 操作方法
    const createFolder = (params: CreateFolderParams): FolderItem => {
      // 检查同名文件夹并生成唯一名称
      let name = params.name
      let counter = 1
      
      while (folders.value.some(folder => folder.name === name)) {
        name = `${params.name} (${counter})`
        counter++
      }

      const now = new Date().toLocaleString()
      const newFolder: FolderItem = {
        id: nanoid(),
        name,
        description: params.description || '',
        createTime: now,
        updateTime: now,
        color: params.color || '#3b82f6',
        icon: params.icon || 'Folder',
        isSystem: false
      }

      folders.value.push(newFolder)
      return newFolder
    }

    const updateFolder = (params: UpdateFolderParams): boolean => {
      const index = folders.value.findIndex(folder => folder.id === params.id)
      if (index === -1) return false

      const folder = folders.value[index]
      
      // 系统文件夹不允许修改名称
      if (folder.isSystem && params.name) {
        return false
      }

      const updatedFolder = {
        ...folder,
        ...params,
        updateTime: new Date().toLocaleString()
      }

      folders.value[index] = updatedFolder
      return true
    }

    const deleteFolder = (folderId: string): boolean => {
      const folder = folders.value.find(f => f.id === folderId)
      
      // 系统文件夹不可删除
      if (!folder || folder.isSystem) {
        return false
      }

      const index = folders.value.findIndex(f => f.id === folderId)
      if (index === -1) return false

      // 删除文件夹下的所有工作流
      const workflowStore = useWorkflowStore()
      workflowStore.deleteWorkflowsByFolder(folderId)

      // 删除文件夹
      folders.value.splice(index, 1)
      
      // 如果删除的是当前文件夹，切换到"全部"
      if (currentFolderId.value === folderId) {
        currentFolderId.value = 'all'
      }
      
      return true
    }

    const duplicateFolder = (folderId: string): FolderItem | null => {
      const folder = folders.value.find(f => f.id === folderId)
      if (!folder || folder.isSystem) return null

      return createFolder({
        name: `${folder.name} - 副本`,
        description: folder.description,
        color: folder.color,
        icon: folder.icon
      })
    }

    // 设置当前文件夹
    const setCurrentFolder = (folderId: string) => {
      const folder = folders.value.find(f => f.id === folderId)
      if (folder) {
        currentFolderId.value = folderId
      }
    }

    // 检查文件夹名称是否已存在
    const isFolderNameExists = (name: string, excludeId?: string): boolean => {
      return folders.value.some(folder => 
        folder.name === name && folder.id !== excludeId
      )
    }

    // 获取文件夹的完整信息（包含工作流数量）
    const getFolderWithStats = (folderId: string) => {
      const folder = folders.value.find(f => f.id === folderId)
      if (!folder) return null

      return {
        ...folder,
        workflowCount: getFolderWorkflowCount(folderId)
      }
    }

    // 获取所有文件夹的统计信息
    const getAllFoldersWithStats = () => {
      return folders.value.map(folder => ({
        ...folder,
        workflowCount: getFolderWorkflowCount(folder.id)
      }))
    }

    // 重置到默认状态
    const resetToDefault = () => {
      folders.value = [
        {
          id: 'all',
          name: '全部',
          description: '显示所有工作流',
          createTime: new Date().toLocaleString(),
          updateTime: new Date().toLocaleString(),
          icon: 'FolderOpen',
          isSystem: true
        }
      ]
      currentFolderId.value = 'all'
    }

    return {
      // 状态
      folders,
      currentFolderId,
      
      // 计算属性
      currentFolder,
      userFolders,
      folderCount,
      
      // 方法
      getFolderWorkflowCount,
      createFolder,
      updateFolder,
      deleteFolder,
      duplicateFolder,
      setCurrentFolder,
      isFolderNameExists,
      getFolderWithStats,
      getAllFoldersWithStats,
      resetToDefault
    }
  },
  {
    persist: createPersistConfig('folder')
  }
)
