// Workflow related
export default {
  // Basic operations
  title: 'Workflow Management',
  create: 'Create Workflow',
  edit: 'Edit Workflow',
  delete: 'Delete Workflow',
  duplicate: 'Duplicate Workflow',
  run: 'Run Workflow',
  stop: 'Stop Workflow',
  pause: 'Pause Workflow',
  resume: 'Resume Workflow',
  
  // Status
  status: {
    idle: 'Idle',
    running: 'Running',
    paused: 'Paused',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
    pending: 'Pending',
  },
  
  // Node related
  node: {
    add: 'Add Node',
    delete: 'Delete Node',
    edit: 'Edit Node',
    connect: 'Connect Node',
    disconnect: 'Disconnect',
    configure: 'Configure Node',
  },
  
  // Properties panel
  properties: {
    name: 'Name',
    description: 'Description',
    type: 'Type',
    input: 'Input',
    output: 'Output',
    parameters: 'Parameters',
    settings: 'Settings',
  },
  
  // Execution related
  execution: {
    start_time: 'Start Time',
    end_time: 'End Time',
    duration: 'Duration',
    progress: 'Progress',
    logs: 'Execution Logs',
    result: 'Result',
  },
}
