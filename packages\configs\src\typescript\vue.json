{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2020", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "useDefineForClassFields": true, "jsx": "preserve", "jsxImportSource": "vue", "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitOverride": true, "noUncheckedIndexedAccess": false, "allowJs": true, "checkJs": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "importHelpers": true, "skipLibCheck": true, "verbatimModuleSyntax": false}, "include": ["src/**/*.ts", "src/**/*.vue", "src/**/*.tsx", "vite.config.ts", "electron.vite.config.ts", "vitest.config.ts", "tailwind.config.ts", "components.json"], "exclude": ["node_modules", "dist", "build", "out", "release", "**/*.test.*", "**/*.spec.*"]}