<template>
  <div class="space-y-6">
    <!-- 启用缓存 -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <Label class="text-sm font-medium">{{ $t('settings.cache_options.enable_cache') }}</Label>
        <p class="text-xs text-muted-foreground">
          {{ $t('settings.cache_options.enable_cache_desc') }}
        </p>
      </div>
      <Switch
        :checked="cacheSettings.enableCache"
        @update:checked="updateCache('enableCache', $event)"
      />
    </div>

    <Separator />

    <!-- 缓存大小 -->
    <div class="space-y-3">
      <div class="flex items-center justify-between">
        <Label class="text-sm font-medium">{{ $t('settings.cache_options.cache_size') }}</Label>
        <span class="text-sm text-muted-foreground">{{ cacheSettings.cacheSize }}MB</span>
      </div>
      <div class="space-y-4">
        <Slider
          :model-value="[cacheSettings.cacheSize]"
          :max="1000"
          :min="50"
          :step="50"
          :disabled="!cacheSettings.enableCache"
          class="flex-1"
          @update:model-value="updateCache('cacheSize', $event[0])"
        />
        <div class="grid grid-cols-4 gap-2">
          <Button
            v-for="size in presetSizes"
            :key="size"
            :variant="cacheSettings.cacheSize === size ? 'default' : 'outline'"
            :disabled="!cacheSettings.enableCache"
            size="sm"
            @click="updateCache('cacheSize', size)"
          >
            {{ size }}MB
          </Button>
        </div>
      </div>
    </div>

    <Separator />

    <!-- 自动清理缓存 -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <Label class="text-sm font-medium">{{ $t('settings.cache_options.auto_clear_cache') }}</Label>
        <p class="text-xs text-muted-foreground">
          {{ $t('settings.cache_options.auto_clear_cache_desc') }}
        </p>
      </div>
      <Switch
        :checked="cacheSettings.autoClearCache"
        :disabled="!cacheSettings.enableCache"
        @update:checked="updateCache('autoClearCache', $event)"
      />
    </div>

    <Separator />

    <!-- 退出时清理缓存 -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <Label class="text-sm font-medium">{{ $t('settings.cache_options.clear_cache_on_exit') }}</Label>
        <p class="text-xs text-muted-foreground">
          {{ $t('settings.cache_options.clear_cache_on_exit_desc') }}
        </p>
      </div>
      <Switch
        :checked="cacheSettings.clearCacheOnExit"
        :disabled="!cacheSettings.enableCache"
        @update:checked="updateCache('clearCacheOnExit', $event)"
      />
    </div>

    <Separator />

    <!-- 缓存操作 -->
    <div class="space-y-3">
      <Label class="text-sm font-medium">{{ $t('settings.cache_options.cache_actions') }}</Label>
      <div class="flex flex-wrap gap-2">
        <Button
          variant="outline"
          size="sm"
          :disabled="!cacheSettings.enableCache"
          @click="clearCache"
        >
          <MattIcon name="Trash2" class="mr-2 h-4 w-4" />
          {{ $t('settings.cache_options.clear_cache') }}
        </Button>
        <Button
          variant="outline"
          size="sm"
          :disabled="!cacheSettings.enableCache"
          @click="viewCacheInfo"
        >
          <MattIcon name="Info" class="mr-2 h-4 w-4" />
          {{ $t('settings.cache_options.view_cache_info') }}
        </Button>
      </div>
    </div>

    <!-- 缓存信息显示 -->
    <div v-if="showCacheInfo" class="rounded-lg border bg-muted/50 p-4">
      <div class="space-y-2">
        <div class="flex justify-between text-sm">
          <span>{{ $t('settings.cache_options.current_cache_size') }}:</span>
          <span class="font-mono">{{ currentCacheSize }}MB</span>
        </div>
        <div class="flex justify-between text-sm">
          <span>{{ $t('settings.cache_options.cache_entries') }}:</span>
          <span class="font-mono">{{ cacheEntries }}</span>
        </div>
        <div class="flex justify-between text-sm">
          <span>{{ $t('settings.cache_options.last_cleared') }}:</span>
          <span class="font-mono">{{ lastCleared || t('common.never') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { useSettingsStore, type CacheSettings } from '@/store'

const { t } = useI18n()
const settingsStore = useSettingsStore()

const showCacheInfo = ref(false)
const currentCacheSize = ref(0)
const cacheEntries = ref(0)
const lastCleared = ref<string | null>(null)

const cacheSettings = computed(() => settingsStore.cache)

const presetSizes = [100, 200, 500, 1000]

const updateCache = (key: keyof CacheSettings, value: any) => {
  settingsStore.updateCache({ [key]: value })
}

const clearCache = async () => {
  try {
    // 这里应该调用实际的缓存清理逻辑
    // await window.electronAPI.clearCache()
    
    currentCacheSize.value = 0
    cacheEntries.value = 0
    lastCleared.value = new Date().toLocaleString()
    
    console.log(t('settings.cache_options.cache_cleared_success'))
  } catch (error) {
    console.error(t('settings.cache_options.cache_cleared_failed'), error)
  }
}

const viewCacheInfo = async () => {
  try {
    // 这里应该调用实际的缓存信息获取逻辑
    // const info = await window.electronAPI.getCacheInfo()
    
    // 模拟数据
    currentCacheSize.value = Math.floor(Math.random() * cacheSettings.value.cacheSize)
    cacheEntries.value = Math.floor(Math.random() * 1000)
    
    showCacheInfo.value = !showCacheInfo.value
  } catch (error) {
    console.error(t('settings.cache_options.get_cache_info_failed'), error)
  }
}
</script>
