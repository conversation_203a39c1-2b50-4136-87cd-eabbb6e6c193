import { resolve } from 'node:path'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import type { UserConfig, Plugin } from 'vite'

/**
 * Vite 配置选项接口
 */
export interface BuildViteConfigOptions {
  /** 应用名称 */
  appName?: string
  /** 自定义插件 */
  plugins?: Plugin[]
  /** 路径别名 */
  alias?: Record<string, string>
  /** 其他自定义选项 */
  [key: string]: any
}

/**
 * 基础 Vite 配置
 * 为所有应用提供统一的 Vite 配置
 */
export const createViteConfig = (options: BuildViteConfigOptions = {}): UserConfig => {
  const { appName = '', plugins = [], alias = {}, ...customOptions } = options

  const baseAlias = {
    '@': resolve(process.cwd(), 'src'),
    '@mattverse/shared': resolve(process.cwd(), '../../packages/shared/src'),
    '@mattverse/ui': resolve(process.cwd(), '../../packages/mattverse-ui/src'),
    '@mattverse/flow': resolve(process.cwd(), '../../packages/mattverse-flow/src'),
    '@mattverse/configs': resolve(process.cwd(), '../../packages/configs/src'),
    ...alias,
  }

  return {
    plugins: [vue(), tailwindcss(), ...plugins],
    resolve: {
      alias: baseAlias,
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@mattverse/configs/src/styles/theme.css";`,
        },
      },
    },
    build: {
      rollupOptions: {
        external: ['electron'],
      },
    },
    ...customOptions,
  }
}

/**
 * 预设配置工厂函数
 * 应用可以使用这些工厂函数创建自己的配置
 */
export const vitePresets = {
  /** 默认配置 */
  default: (appName: string, options: Omit<BuildViteConfigOptions, 'appName'> = {}) =>
    createViteConfig({ appName, ...options }),

  /** Electron 渲染进程配置 */
  electronRenderer: (appName: string, options: Omit<BuildViteConfigOptions, 'appName'> = {}) =>
    createViteConfig({
      appName,
      alias: {
        '@': resolve(process.cwd(), 'src/renderer/src'),
        ...options.alias,
      },
      ...options,
    }),

  /** 自定义配置 */
  custom: (options: BuildViteConfigOptions) =>
    createViteConfig(options),
}

// 默认导出基础配置
export default createViteConfig()
