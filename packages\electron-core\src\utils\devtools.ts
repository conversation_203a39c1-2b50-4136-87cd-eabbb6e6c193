/**
 * 开发者工具管理器
 * 统一管理 Electron 开发者工具的安装和配置
 */
import { app, BrowserWindow } from 'electron'
import { logger } from './logger'

// 开发者工具扩展 ID 常量
export const DEVTOOLS_EXTENSIONS = {
  REACT_DEVELOPER_TOOLS: 'fmkadmapgofadopljbjfkapdkoienihi',
  REDUX_DEVTOOLS: 'lmhkpmbekcpmknklioeibfkpmmfibljd',
  VUE_DEVTOOLS: 'nhdogjmejiglipccpnnnanhbledajbpd',
  APOLLO_DEVTOOLS: 'jdkknkkbebbapilgoeccciglkfbmbnfm',
  MOBX_DEVTOOLS: 'pfgnfdagidkfgccljigdamigbcnndkod',
} as const

export type DevToolsExtension = keyof typeof DEVTOOLS_EXTENSIONS

/**
 * 开发者工具配置选项
 */
export interface DevToolsConfig {
  /** 是否启用开发者工具 */
  enabled?: boolean
  /** 要安装的扩展列表 */
  extensions?: DevToolsExtension[]
  /** 是否在窗口创建时自动打开开发者工具 */
  autoOpen?: boolean
  /** 是否在生产环境中也启用（不推荐） */
  forceInProduction?: boolean
  /** 自定义扩展 ID 列表 */
  customExtensions?: string[]
}

/**
 * 开发者工具管理器类
 */
export class DevToolsManager {
  private config: Required<DevToolsConfig>
  private installedExtensions: Set<string> = new Set()

  constructor(config: DevToolsConfig = {}) {
    this.config = {
      enabled: true,
      extensions: ['VUE_DEVTOOLS'],
      autoOpen: true,
      forceInProduction: false,
      customExtensions: [],
      ...config,
    }
  }

  /**
   * 检查是否应该启用开发者工具
   */
  private shouldEnable(): boolean {
    if (!this.config.enabled) {
      return false
    }

    // 在开发环境或未打包的应用中启用
    const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged

    // 如果强制在生产环境启用，则始终启用
    if (this.config.forceInProduction) {
      return true
    }

    return isDevelopment
  }

  /**
   * 安装开发者工具扩展
   */
  async installExtensions(): Promise<void> {
    if (!this.shouldEnable()) {
      logger.info('DevTools: 跳过扩展安装（未启用或在生产环境）')
      return
    }

    try {
      // 动态导入 electron-devtools-installer
      const installer = await import('electron-devtools-installer')
      const installExtension = installer.default

      // 安装预定义扩展
      for (const extensionName of this.config.extensions) {
        try {
          // 直接使用扩展 ID 安装
          const extensionId = DEVTOOLS_EXTENSIONS[extensionName]
          if (extensionId) {
            await installExtension(extensionId, {
              loadExtensionOptions: { allowFileAccess: true },
              forceDownload: false, // 使用缓存
            })
            this.installedExtensions.add(extensionId)
            logger.info(`DevTools: 已安装 ${extensionName}`)
          } else {
            logger.warn(`DevTools: 未找到扩展 ${extensionName} 的 ID`)
          }
        } catch (error) {
          logger.warn(`DevTools: 安装扩展 ${extensionName} 失败:`, error)
        }
      }

      // 安装自定义扩展
      for (const extensionId of this.config.customExtensions) {
        try {
          await installExtension(extensionId, {
            loadExtensionOptions: { allowFileAccess: true },
            forceDownload: false,
          })
          this.installedExtensions.add(extensionId)
          logger.info(`DevTools: 已安装自定义扩展 ${extensionId}`)
        } catch (error) {
          logger.warn(`DevTools: 安装自定义扩展 ${extensionId} 失败:`, error)
        }
      }

      logger.info(`DevTools: 扩展安装完成，共安装 ${this.installedExtensions.size} 个扩展`)
    } catch (error) {
      logger.error('DevTools: 扩展安装过程中发生错误:', error)
    }
  }

  /**
   * 为窗口设置开发者工具
   */
  setupForWindow(window: BrowserWindow): void {
    if (!this.shouldEnable()) {
      return
    }

    // 窗口准备就绪时的处理
    window.webContents.once('dom-ready', () => {
      if (this.config.autoOpen) {
        window.webContents.openDevTools()
        logger.info('DevTools: 自动打开开发者工具')
      }
    })

    // 添加键盘快捷键支持
    window.webContents.on('before-input-event', (_, input) => {
      // F12 键切换开发者工具
      if (input.key === 'F12') {
        if (window.webContents.isDevToolsOpened()) {
          window.webContents.closeDevTools()
        } else {
          window.webContents.openDevTools()
        }
        logger.info('DevTools: 通过 F12 键切换开发者工具')
      }
    })
  }

  /**
   * 获取已安装的扩展列表
   */
  getInstalledExtensions(): string[] {
    return Array.from(this.installedExtensions)
  }

  /**
   * 检查特定扩展是否已安装
   */
  isExtensionInstalled(extensionId: string): boolean {
    return this.installedExtensions.has(extensionId)
  }

  /**
   * 清理开发者工具相关资源
   */
  cleanup(): void {
    this.installedExtensions.clear()
    logger.info('DevTools: 清理完成')
  }
}

/**
 * 创建开发者工具管理器实例
 */
export function createDevToolsManager(config?: DevToolsConfig): DevToolsManager {
  return new DevToolsManager(config)
}

/**
 * 便捷函数：快速安装 Vue 开发者工具
 */
export async function installVueDevTools(): Promise<void> {
  const manager = createDevToolsManager({
    extensions: ['VUE_DEVTOOLS'],
    autoOpen: true,
  })
  await manager.installExtensions()
}

/**
 * 便捷函数：快速安装 React 开发者工具
 */
export async function installReactDevTools(): Promise<void> {
  const manager = createDevToolsManager({
    extensions: ['REACT_DEVELOPER_TOOLS', 'REDUX_DEVTOOLS'],
    autoOpen: true,
  })
  await manager.installExtensions()
}
