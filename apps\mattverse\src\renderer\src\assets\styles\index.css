/* 导入 Tailwind CSS */
@import 'tailwindcss';
/* 导入主题文件 */
@import '@mattverse/shared/styles/theme.css';
/* 导入 UI 组件样式 */
@import '@mattverse/mattverse-ui/styles';

/* 动态字体设置支持 */
:root {
  --base-font-size: 14px;
}

/* 确保 body 使用动态字体设置 */
body {
  font-family: var(--font-display) !important;
  font-size: var(--base-font-size) !important;
}

/* 确保所有文本元素都能继承字体设置 */
* {
  font-family: inherit;
}

/* 为不同的文本大小提供相对单位支持 */
.text-xs { font-size: calc(var(--base-font-size) * 0.75); }
.text-sm { font-size: calc(var(--base-font-size) * 0.875); }
.text-base { font-size: var(--base-font-size); }
.text-lg { font-size: calc(var(--base-font-size) * 1.125); }
.text-xl { font-size: calc(var(--base-font-size) * 1.25); }
.text-2xl { font-size: calc(var(--base-font-size) * 1.5); }
.text-3xl { font-size: calc(var(--base-font-size) * 1.875); }
.text-4xl { font-size: calc(var(--base-font-size) * 2.25); }
