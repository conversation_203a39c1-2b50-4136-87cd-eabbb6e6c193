import { defineStore } from 'pinia'
import { ref } from 'vue'
import { createPersistConfig } from '@/store/plugins/persist-config'
import { serverService ,type Server,ServerUsage,ServerStatus } from '@mattverse/shared'
/**
 * 服务器状态管理
 */
export const useServerStore = defineStore(
  'server',
  () => {
    const loadingUsage = ref(false)
    const refreshTimer = ref<number>(5) // 服务列表刷新间隔（秒）
    const servers = ref<Server[]>([])
    const serverUsages = ref<ServerUsage[]>([])


    //刷新服务器列表
    const updateServerList = async (serverId?: string) => {
      const res = await serverService.getServerList(serverId)
      if (res.status === 'Success' && res.serverResult?.serverList) {
        servers.value = res.serverResult.serverList
      }
    }

     // 刷新服务器状态
    const updateServerUsage = async (serverId: string) => {
      loadingUsage.value = true
      const res = await serverService.getServerUsage(serverId)
      if (res.status === 'Success' && res.serverUsage) {
        const server = servers.value.find((s) => s.serverId === serverId)
        if (!server) return
        const serverUsage = res.serverUsage
        serverUsage.serverId = server.serverId
        serverUsage.serverName = server.serverName
        const index = serverUsages.value.findIndex((s) => s.serverId === serverUsage.serverId)
        if (index !== -1) {
          serverUsages.value = serverUsages.value.map((s) =>
            s.serverId === serverUsage.serverId ? serverUsage : s,
          )
        } else {
          serverUsages.value.push(serverUsage)
        }
      }
      loadingUsage.value = false
    }

     // 获取状态显示文本
    const getStatusText = (status: ServerStatus) => {
      const statusText: Record<ServerStatus, string> = {
        Running: '正常运行',
        Stopped: '已停止运行',
        Expired: '已过期',
        Overloaded: '服务器超载',
        Stay: '原始状态',
      }
      return statusText[status] || status
    }

    // 获取状态对应的样式
    const getStatusClass = (status: ServerStatus) => {
      const classes: Record<ServerStatus, string> = {
        Running: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
        Stopped: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
        Expired: 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300',
        Overloaded: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300',
        Stay: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300',
      }
      return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
    }
     // 设置刷新间隔
    const setRefreshTimer = (interval: number) => {
      refreshTimer.value = interval
    }

    return {
      // 状态
      loadingUsage,
      servers,
      serverUsages,
      refreshTimer,
      // 计算属性

      // 方法
      updateServerUsage,
      getStatusText,
      getStatusClass,
      updateServerList,
      setRefreshTimer
    }
  },
  {
    persist: createPersistConfig('server'),
  }
)
