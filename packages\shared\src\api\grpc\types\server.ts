export type ServerStatus = 'Running' | 'Stopped' | 'Expired' | 'Overloaded' | 'Stay'

export interface Server {
  serverId: string
  serverName: string
  url: string
  serverStatus: ServerStatus
  serverType: string
  region: string
  version: string
  accessLevel: number
  createTime: number
  updateTime: number
}

export interface ServerUsage {
  serverId: string
  serverName: string
  cpuUsage: number
  memoryStatus: {
    totalMemory: number
    freeMemory: number
    usedMemory: number
    unit: string
  }
  gpuStatusList: Array<{
    index: number
    model: string
    usage: number
    usedMemory: string
    freeMemory: string
    totalMemory: string
    memoryUsage: number
  }>
}
