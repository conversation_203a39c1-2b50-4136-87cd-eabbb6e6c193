<template>
  <div id="app" class="w-full h-full flex justify-center items-center">
    <ScrollArea class="h-full w-full rounded-md">
      <div class="min-h-screen flex items-center justify-center mattverse-bg-gradient">
        <div class="text-center space-y-8 p-8">
          <div class="space-y-4">
            <h1 class="text-6xl font-bold text-blue-300">HighPower</h1>

            <!-- Electron API 测试区域 -->
            <div class="space-y-4 max-w-2xl mx-auto p-6 bg-white/10 rounded-lg backdrop-blur-sm">
              <h2 class="text-2xl font-bold text-center text-white mb-4">
                HighPower Electron Core API 测试
              </h2>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                <!-- 应用信息 -->
                <div class="p-4 bg-black/20 rounded">
                  <h3 class="text-lg font-semibold text-blue-200 mb-2">应用信息</h3>
                  <div class="space-y-1 text-sm text-white">
                    <p>
                      <span class="text-gray-300">应用名称:</span>
                      {{ appInfo.appName || '加载中...' }}
                    </p>
                    <p>
                      <span class="text-gray-300">版本号:</span>
                      {{ appInfo.version || '加载中...' }}
                    </p>
                    <p>
                      <span class="text-gray-300">特性:</span>
                      {{ appInfo.features?.join(', ') || '加载中...' }}
                    </p>
                  </div>
                </div>

                <!-- 系统信息 -->
                <div class="p-4 bg-black/20 rounded">
                  <h3 class="text-lg font-semibold text-blue-200 mb-2">系统信息</h3>
                  <div class="space-y-1 text-sm text-white">
                    <p>
                      <span class="text-gray-300">平台:</span>
                      {{ systemInfo.platform || '加载中...' }}
                    </p>
                    <p>
                      <span class="text-gray-300">Node.js:</span>
                      {{ systemInfo.nodeVersion || '加载中...' }}
                    </p>
                    <p>
                      <span class="text-gray-300">Electron:</span>
                      {{ systemInfo.electronVersion || '加载中...' }}
                    </p>
                  </div>
                </div>

                <!-- 高性能计算测试 -->
                <div class="p-4 bg-black/20 rounded">
                  <h3 class="text-lg font-semibold text-blue-200 mb-2">计算任务测试</h3>
                  <div class="space-y-2">
                    <Button @click="testStartJob" size="sm" class="w-full"> 启动计算任务 </Button>
                    <Button @click="testGetJobStatus" size="sm" class="w-full">
                      查询任务状态
                    </Button>
                    <Button @click="testGPUInfo" size="sm" class="w-full"> 获取GPU信息 </Button>
                  </div>
                </div>

                <!-- 测试结果 -->
                <div class="p-4 bg-black/20 rounded">
                  <h3 class="text-lg font-semibold text-blue-200 mb-2">测试结果</h3>
                  <div class="text-sm text-white">
                    <pre class="whitespace-pre-wrap">{{ testResult || '点击按钮进行测试...' }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- gRPC 状态区域 -->
          <div class="space-y-4 max-w-2xl mx-auto p-6 bg-white/10 rounded-lg backdrop-blur-sm">
            <h2 class="text-2xl font-bold text-center text-white mb-4">gRPC 连接状态</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <!-- gRPC 连接信息 -->
              <div class="p-4 bg-black/20 rounded">
                <h3 class="text-lg font-semibold text-blue-200 mb-2">连接信息</h3>
                <div class="space-y-1 text-sm text-white">
                  <p>
                    <span class="text-gray-300">状态:</span>
                    <span :class="grpcStatus.connected ? 'text-green-400' : 'text-red-400'">
                      {{ grpcStatus.connected ? '已连接' : '未连接' }}
                    </span>
                  </p>
                  <p>
                    <span class="text-gray-300">初始化:</span>
                    <span :class="grpcStatus.initialized ? 'text-green-400' : 'text-yellow-400'">
                      {{ grpcStatus.initialized ? '已初始化' : '未初始化' }}
                    </span>
                  </p>
                  <p>
                    <span class="text-gray-300">服务器:</span>
                    {{ grpcStatus.url || '未知' }}
                  </p>
                  <p>
                    <span class="text-gray-300">用户ID:</span>
                    {{ grpcStatus.userId || '未知' }}
                  </p>
                </div>
              </div>

              <!-- gRPC 测试按钮 -->
              <div class="p-4 bg-black/20 rounded">
                <h3 class="text-lg font-semibold text-blue-200 mb-2">gRPC 测试</h3>
                <div class="space-y-2">
                  <Button @click="initGrpc" size="sm" class="w-full" :disabled="grpcLoading">
                    {{ grpcLoading ? '初始化中...' : '初始化 gRPC' }}
                  </Button>
                  <Button
                    @click="testGrpcPing"
                    size="sm"
                    class="w-full"
                    :disabled="!grpcStatus.initialized"
                  >
                    Ping 测试
                  </Button>
                  <Button
                    @click="testGrpcComputeTask"
                    size="sm"
                    class="w-full"
                    :disabled="!grpcStatus.initialized"
                  >
                    通用调用测试
                  </Button>
                  <Button @click="refreshGrpcStatus" size="sm" class="w-full"> 刷新状态 </Button>
                </div>
              </div>

              <!-- gRPC 测试结果 -->
              <div class="p-4 bg-black/20 rounded md:col-span-2">
                <h3 class="text-lg font-semibold text-blue-200 mb-2">gRPC 测试结果</h3>
                <div class="text-sm text-white">
                  <pre class="whitespace-pre-wrap max-h-40 overflow-y-auto">{{
                    grpcTestResult || '点击按钮进行 gRPC 测试...'
                  }}</pre>
                </div>
              </div>
            </div>
          </div>

          <!-- 字体测试区域 -->
          <div class="space-y-6 max-w-4xl mx-auto p-6 bg-white/10 rounded-lg backdrop-blur-sm">
            <h2 class="text-2xl font-bold text-center text-white mb-4">字体测试展示</h2>

            <!-- 阿里巴巴字体 -->
            <div class="space-y-3">
              <h3 class="text-lg font-semibold text-blue-200">阿里巴巴字体系列</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="p-4 bg-black/20 rounded">
                  <p class="text-sm text-gray-300 mb-2">阿里巴巴普惠体 (AlibabaPuHuiTi)</p>
                  <p class="font-alibaba-puhuiti text-lg text-white">
                    这是阿里巴巴普惠体的测试文本 - The quick brown fox jumps over the lazy dog
                  </p>
                </div>
                <div class="p-4 bg-black/20 rounded">
                  <p class="text-sm text-gray-300 mb-2">阿里巴巴香港字体 (AlibabaSansHK)</p>
                  <p class="font-alibaba-hk text-lg text-white">
                    這是阿里巴巴香港字體的測試文本 - The quick brown fox jumps over the lazy dog
                  </p>
                </div>
              </div>
            </div>

            <!-- Noto Sans SC 系列 -->
            <div class="space-y-3">
              <h3 class="text-lg font-semibold text-blue-200">Noto Sans SC 系列</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">Light (300)</p>
                  <p class="font-noto-light font-light text-base text-white">轻量字体测试</p>
                </div>
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">Regular (400)</p>
                  <p class="font-noto-regular font-normal text-base text-white">常规字体测试</p>
                </div>
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">Medium (500)</p>
                  <p class="font-noto-medium font-medium text-base text-white">中等字体测试</p>
                </div>
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">ExtraBold (800)</p>
                  <p class="font-noto-extrabold font-extrabold text-base text-white">
                    超粗字体测试
                  </p>
                </div>
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">Black (900)</p>
                  <p class="font-noto-black font-black text-base text-white">黑体字体测试</p>
                </div>
              </div>
            </div>

            <!-- Source Han Sans 系列 -->
            <div class="space-y-3">
              <h3 class="text-lg font-semibold text-blue-200">Source Han Sans 系列</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">Source Han Sans</p>
                  <p class="font-source-han text-base text-white">思源黑体通用版</p>
                </div>
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">简体中文 (SC)</p>
                  <p class="font-source-han-sc text-base text-white">思源黑体简体中文</p>
                </div>
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">繁体中文 (TC)</p>
                  <p class="font-source-han-tc text-base text-white">思源黑體繁體中文</p>
                </div>
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">香港 (HC)</p>
                  <p class="font-source-han-hc text-base text-white">思源黑體香港版</p>
                </div>
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">香港 (HK)</p>
                  <p class="font-source-han-hk text-base text-white">思源黑體香港版本</p>
                </div>
                <div class="p-3 bg-black/20 rounded">
                  <p class="text-xs text-gray-400 mb-1">韩文 (K)</p>
                  <p class="font-source-han-k text-base text-white">Source Han Sans Korean</p>
                </div>
              </div>
            </div>

            <!-- 字体对比测试 -->
            <div class="space-y-3">
              <h3 class="text-lg font-semibold text-blue-200">字体对比测试</h3>
              <div class="space-y-2">
                <p class="text-sm text-gray-300">相同内容，不同字体效果对比：</p>
                <div class="space-y-2">
                  <p class="font-alibaba-puhuiti text-lg text-white">
                    阿里巴巴普惠体：优雅简洁的现代中文字体设计
                  </p>
                  <p class="font-noto-regular font-normal text-lg text-white">
                    Noto Sans SC：Google 开源的中文字体
                  </p>
                  <p class="font-source-han-sc text-lg text-white">
                    思源黑体：Adobe 与 Google 联合开发
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 测试自动导入的组件 -->
          <div class="space-y-4 max-w-md mx-auto">
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ message }}
            </div>
            <Input type="email" placeholder="Email" />
            <img :src="email" />
            <MattIcon name="X" :width="20" :height="20" />
            <MattSvg :name="email" />

            <div class="space-y-3">
              <Badge v-if="isVisible" variant="secondary">自动导入的 Badge</Badge>

              <div class="flex gap-2 justify-center">
                <Button @click="isVisible = !isVisible">
                  {{ isVisible ? '隐藏' : '显示' }} Badge
                </Button>
                <Button variant="secondary">次要按钮</Button>
                <Button variant="outline">轮廓按钮</Button>
              </div>
            </div>
            <Alert>
              <MattIcon name="Rocket" :width="20" :height="20" />
              <AlertTitle>Heads up!</AlertTitle>
              <AlertDescription>
                You can add components to your app using the cli.
              </AlertDescription>
            </Alert>

            <Sheet>
              <SheetTrigger as-child>
                <Button variant="outline"> Open </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Edit profile</SheetTitle>
                  <SheetDescription>
                    Make changes to your profile here. Click save when you're done.
                  </SheetDescription>
                </SheetHeader>
                <div class="grid gap-4 py-4">
                  <div class="grid grid-cols-4 items-center gap-4">
                    <Label for="name" class="text-right"> Name </Label>
                    <Input id="name" value="Pedro Duarte" class="col-span-3" />
                  </div>
                  <div class="grid grid-cols-4 items-center gap-4">
                    <Label for="username" class="text-right"> Username </Label>
                    <Input id="username" value="@peduarte" class="col-span-3" />
                  </div>
                </div>
                <SheetFooter>
                  <SheetClose as-child>
                    <Button type="submit"> Save changes </Button>
                  </SheetClose>
                </SheetFooter>
              </SheetContent>
            </Sheet>

            <div>
              <div class="space-y-1">
                <h4 class="text-sm font-medium leading-none">Radix Primitives</h4>
                <p class="text-sm text-muted-foreground">An open-source UI component library.</p>
              </div>
              <Separator class="my-4" />
              <div class="flex h-5 items-center space-x-4 text-sm">
                <div>Blog</div>
                <Separator orientation="vertical" />
                <div>Docs</div>
                <Separator orientation="vertical" />
                <div>Source</div>
              </div>
            </div>

            <div class="flex items-center space-x-4">
              <Skeleton class="h-12 w-12 rounded-full" />
              <div class="space-y-2">
                <Skeleton class="h-4 w-[250px]" />
                <Skeleton class="h-4 w-[200px]" />
              </div>
            </div>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger as-child>
                  <Button variant="outline"> Hover </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Add to library</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <!-- <Sidebar>
            <SidebarHeader />
            <SidebarContent>
              <SidebarGroup />
              <SidebarGroup />
            </SidebarContent>
            <SidebarFooter />
          </Sidebar> -->

            <AlertDialog>
              <AlertDialogTrigger as-child>
                <Button variant="outline"> Show Dialog </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete your account and
                    remove your data from our servers.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction>Continue</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <Avatar>
              <AvatarImage src="https://github.com/unovue.png" alt="@unovue" />
              <AvatarFallback>CN</AvatarFallback>
            </Avatar>

            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/"> Home </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator>
                  <Slash />
                </BreadcrumbSeparator>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/docs/components/accordion.html">
                    Components
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator>
                  <Slash />
                </BreadcrumbSeparator>
                <BreadcrumbItem>
                  <BreadcrumbPage>Breadcrumb</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            <Accordion type="single" class="w-full" collapsible :default-value="defaultValue">
              <AccordionItem v-for="item in accordionItems" :key="item.value" :value="item.value">
                <AccordionTrigger>{{ item.title }}</AccordionTrigger>
                <AccordionContent>
                  {{ item.content }}
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            <Card class="w-[350px]">
              <CardHeader>
                <CardTitle>Create project</CardTitle>
                <CardDescription>Deploy your new project in one-click.</CardDescription>
              </CardHeader>
              <CardContent>
                <form>
                  <div class="grid items-center w-full gap-4">
                    <div class="flex flex-col space-y-1.5">
                      <Label for="name">Name</Label>
                      <Input id="name" placeholder="Name of your project" />
                    </div>
                    <div class="flex flex-col space-y-1.5">
                      <Label for="framework">Framework</Label>
                      <Select>
                        <SelectTrigger id="framework">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent position="popper">
                          <SelectItem value="nuxt"> Nuxt </SelectItem>
                          <SelectItem value="next"> Next.js </SelectItem>
                          <SelectItem value="sveltekit"> SvelteKit </SelectItem>
                          <SelectItem value="astro"> Astro </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </form>
              </CardContent>
              <CardFooter class="flex justify-between px-6 pb-6">
                <Button variant="outline"> Cancel </Button>
                <Button>Deploy</Button>
              </CardFooter>
            </Card>

            <Carousel class="relative w-full max-w-xs">
              <CarouselContent>
                <CarouselItem v-for="(_, index) in 5" :key="index">
                  <div class="p-1">
                    <Card>
                      <CardContent class="flex aspect-square items-center justify-center p-6">
                        <span class="text-4xl font-semibold">{{ index + 1 }}</span>
                      </CardContent>
                    </Card>
                  </div>
                </CarouselItem>
              </CarouselContent>
              <CarouselPrevious />
              <CarouselNext />
            </Carousel>

            <div class="flex items-center space-x-2">
              <Checkbox id="terms" />
              <label
                for="terms"
                class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Accept terms and conditions
              </label>
            </div>

            <Collapsible v-model:open="isOpen" class="w-[350px] space-y-2">
              <div class="flex items-center justify-between space-x-4 px-4">
                <h4 class="text-sm font-semibold">@peduarte starred 3 repositories</h4>
                <CollapsibleTrigger as-child>
                  <Button variant="ghost" size="sm" class="w-9 p-0">
                    <ChevronsUpDown class="h-4 w-4" />
                    <span class="sr-only">Toggle</span>
                  </Button>
                </CollapsibleTrigger>
              </div>
              <div class="rounded-md border px-4 py-3 font-mono text-sm">@radix-ui/primitives</div>
              <CollapsibleContent class="space-y-2">
                <div class="rounded-md border px-4 py-3 font-mono text-sm">@radix-ui/colors</div>
                <div class="rounded-md border px-4 py-3 font-mono text-sm">@stitches/react</div>
              </CollapsibleContent>
            </Collapsible>

            <Combobox by="label">
              <ComboboxAnchor>
                <div class="relative w-full max-w-sm items-center">
                  <ComboboxInput
                    class="pl-9"
                    :display-value="(val) => val?.label ?? ''"
                    placeholder="Select framework..."
                  />
                  <span class="absolute start-0 inset-y-0 flex items-center justify-center px-3">
                    <MattIcon name="Search" class="size-4 text-muted-foreground" />
                  </span>
                </div>
              </ComboboxAnchor>

              <ComboboxList>
                <ComboboxEmpty> No framework found. </ComboboxEmpty>

                <ComboboxGroup>
                  <ComboboxItem
                    v-for="framework in frameworks"
                    :key="framework.value"
                    :value="framework"
                  >
                    {{ framework.label }}

                    <ComboboxItemIndicator>
                      <MattIcon name="check" :class="cn('ml-auto h-4 w-4')" />
                    </ComboboxItemIndicator>
                  </ComboboxItem>
                </ComboboxGroup>
              </ComboboxList>
            </Combobox>

            <Command class="rounded-lg border shadow-md max-w-[450px]">
              <CommandInput placeholder="Type a command or search..." />
              <CommandList>
                <CommandEmpty>No results found.</CommandEmpty>
                <CommandGroup heading="Suggestions">
                  <CommandItem value="calendar">
                    <MattIcon name="Calendar" />
                    <span>Calendar</span>
                  </CommandItem>
                  <CommandItem value="search">
                    <MattIcon name="Smile" />
                    <span>Search Emoji</span>
                  </CommandItem>
                  <CommandItem disabled value="calculator">
                    <MattIcon name="Calculator" />
                    <span>Calculator</span>
                  </CommandItem>
                </CommandGroup>
                <CommandSeparator />
                <CommandGroup heading="Settings">
                  <CommandItem value="profile">
                    <MattIcon name="User" />
                    <span>Profile</span>
                    <CommandShortcut>⌘P</CommandShortcut>
                  </CommandItem>
                  <CommandItem value="billing">
                    <MattIcon name="CreditCard" />
                    <span>Billing</span>
                    <CommandShortcut>⌘B</CommandShortcut>
                  </CommandItem>
                  <CommandItem value="settings">
                    <MattIcon name="Settings" />
                    <span>Settings</span>
                    <CommandShortcut>⌘S</CommandShortcut>
                  </CommandItem>
                </CommandGroup>
              </CommandList>
            </Command>

            <ContextMenu>
              <ContextMenuTrigger
                class="flex h-[150px] w-[300px] items-center justify-center rounded-md border border-dashed text-sm"
              >
                Right click here
              </ContextMenuTrigger>
              <ContextMenuContent class="w-64">
                <ContextMenuItem inset>
                  Back
                  <ContextMenuShortcut>⌘[</ContextMenuShortcut>
                </ContextMenuItem>
                <ContextMenuItem inset disabled>
                  Forward
                  <ContextMenuShortcut>⌘]</ContextMenuShortcut>
                </ContextMenuItem>
                <ContextMenuItem inset>
                  Reload
                  <ContextMenuShortcut>⌘R</ContextMenuShortcut>
                </ContextMenuItem>
                <ContextMenuSub>
                  <ContextMenuSubTrigger inset> More Tools </ContextMenuSubTrigger>
                  <ContextMenuSubContent class="w-48">
                    <ContextMenuItem>
                      Save Page As...
                      <ContextMenuShortcut>⇧⌘S</ContextMenuShortcut>
                    </ContextMenuItem>
                    <ContextMenuItem>Create Shortcut...</ContextMenuItem>
                    <ContextMenuItem>Name Window...</ContextMenuItem>
                    <ContextMenuSeparator />
                    <ContextMenuItem>Developer Tools</ContextMenuItem>
                  </ContextMenuSubContent>
                </ContextMenuSub>
                <ContextMenuSeparator />
                <ContextMenuCheckboxItem :model-value="true">
                  Show Bookmarks Bar
                  <ContextMenuShortcut>⌘⇧B</ContextMenuShortcut>
                </ContextMenuCheckboxItem>
                <ContextMenuCheckboxItem>Show Full URLs</ContextMenuCheckboxItem>
                <ContextMenuSeparator />
                <ContextMenuRadioGroup model-value="pedro">
                  <ContextMenuLabel inset> People </ContextMenuLabel>
                  <ContextMenuSeparator />
                  <ContextMenuRadioItem value="pedro"> Pedro Duarte </ContextMenuRadioItem>
                  <ContextMenuRadioItem value="colm"> Colm Tuite </ContextMenuRadioItem>
                </ContextMenuRadioGroup>
              </ContextMenuContent>
            </ContextMenu>

            <Dialog>
              <DialogTrigger as-child>
                <Button variant="outline"> Share </Button>
              </DialogTrigger>
              <DialogContent class="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Share link</DialogTitle>
                  <DialogDescription>
                    Anyone who has this link will be able to view this.
                  </DialogDescription>
                </DialogHeader>
                <div class="flex items-center space-x-2">
                  <div class="grid flex-1 gap-2">
                    <Label for="link" class="sr-only"> Link </Label>
                    <Input
                      id="link"
                      default-value="https://shadcn-vue.com/docs/installation"
                      read-only
                    />
                  </div>
                  <Button type="submit" size="sm" class="px-3">
                    <span class="sr-only">Copy</span>
                    <Copy class="w-4 h-4" />
                  </Button>
                </div>
                <DialogFooter class="sm:justify-start">
                  <DialogClose as-child>
                    <Button type="button" variant="secondary"> Close </Button>
                  </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Drawer>
              <DrawerTrigger as-child>
                <Button variant="outline"> Open Drawer </Button>
              </DrawerTrigger>
              <DrawerContent>
                <DrawerHeader>
                  <DrawerTitle>Move Goal</DrawerTitle>
                  <DrawerDescription>Set your daily activity goal.</DrawerDescription>
                </DrawerHeader>
                <DrawerClose as-child>
                  <Button variant="outline"> Close </Button>
                </DrawerClose>
              </DrawerContent>
            </Drawer>

            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="outline"> Open </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent class="w-56">
                <DropdownMenuLabel>Panel Position</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup v-model="position">
                  <DropdownMenuRadioItem value="top"> Top </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="bottom"> Bottom </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="right"> Right </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            <HoverCard>
              <HoverCardTrigger as-child>
                <Button variant="link"> @vuejs </Button>
              </HoverCardTrigger>
              <HoverCardContent class="w-80">
                <div class="flex justify-between space-x-4">
                  <Avatar>
                    <AvatarImage src="https://github.com/vuejs.png" />
                    <AvatarFallback>VC</AvatarFallback>
                  </Avatar>
                  <div class="space-y-1">
                    <h4 class="text-sm font-semibold">@vuejs</h4>
                    <p class="text-sm">
                      Progressive JavaScript framework for building modern web interfaces.
                    </p>
                    <div class="flex items-center pt-2">
                      <CalendarIcon class="mr-2 h-4 w-4 opacity-70" />
                      <span class="text-xs text-muted-foreground"> Joined January 2014 </span>
                    </div>
                  </div>
                </div>
              </HoverCardContent>
            </HoverCard>

            <Menubar>
              <MenubarMenu>
                <MenubarTrigger>File</MenubarTrigger>
                <MenubarContent>
                  <MenubarItem> New Tab <MenubarShortcut>⌘T</MenubarShortcut> </MenubarItem>
                  <MenubarItem> New Window <MenubarShortcut>⌘N</MenubarShortcut> </MenubarItem>
                  <MenubarItem disabled> New Incognito Window </MenubarItem>
                  <MenubarSeparator />
                  <MenubarSub>
                    <MenubarSubTrigger>Share</MenubarSubTrigger>
                    <MenubarSubContent>
                      <MenubarItem>Email link</MenubarItem>
                      <MenubarItem>Messages</MenubarItem>
                      <MenubarItem>Notes</MenubarItem>
                    </MenubarSubContent>
                  </MenubarSub>
                  <MenubarSeparator />
                  <MenubarItem> Print... <MenubarShortcut>⌘P</MenubarShortcut> </MenubarItem>
                </MenubarContent>
              </MenubarMenu>
              <MenubarMenu>
                <MenubarTrigger>Edit</MenubarTrigger>
                <MenubarContent>
                  <MenubarItem> Undo <MenubarShortcut>⌘Z</MenubarShortcut> </MenubarItem>
                  <MenubarItem> Redo <MenubarShortcut>⇧⌘Z</MenubarShortcut> </MenubarItem>
                  <MenubarSeparator />
                  <MenubarSub>
                    <MenubarSubTrigger>Find</MenubarSubTrigger>
                    <MenubarSubContent>
                      <MenubarItem>Search the web</MenubarItem>
                      <MenubarSeparator />
                      <MenubarItem>Find...</MenubarItem>
                      <MenubarItem>Find Next</MenubarItem>
                      <MenubarItem>Find Previous</MenubarItem>
                    </MenubarSubContent>
                  </MenubarSub>
                  <MenubarSeparator />
                  <MenubarItem>Cut</MenubarItem>
                  <MenubarItem>Copy</MenubarItem>
                  <MenubarItem>Paste</MenubarItem>
                </MenubarContent>
              </MenubarMenu>
              <MenubarMenu>
                <MenubarTrigger>View</MenubarTrigger>
                <MenubarContent>
                  <MenubarCheckboxItem>Always Show Bookmarks Bar</MenubarCheckboxItem>
                  <MenubarCheckboxItem :model-value="true">
                    Always Show Full URLs
                  </MenubarCheckboxItem>
                  <MenubarSeparator />
                  <MenubarItem inset> Reload <MenubarShortcut>⌘R</MenubarShortcut> </MenubarItem>
                  <MenubarItem disabled inset>
                    Force Reload <MenubarShortcut>⇧⌘R</MenubarShortcut>
                  </MenubarItem>
                  <MenubarSeparator />
                  <MenubarItem inset> Toggle Fullscreen </MenubarItem>
                  <MenubarSeparator />
                  <MenubarItem inset> Hide Sidebar </MenubarItem>
                </MenubarContent>
              </MenubarMenu>
              <MenubarMenu>
                <MenubarTrigger>Profiles</MenubarTrigger>
                <MenubarContent>
                  <MenubarRadioGroup model-value="benoit">
                    <MenubarRadioItem value="andy"> Andy </MenubarRadioItem>
                    <MenubarRadioItem value="benoit"> Benoit </MenubarRadioItem>
                    <MenubarRadioItem value="Luis"> Luis </MenubarRadioItem>
                  </MenubarRadioGroup>
                  <MenubarSeparator />
                  <MenubarItem inset> Edit... </MenubarItem>
                  <MenubarSeparator />
                  <MenubarItem inset> Add Profile... </MenubarItem>
                </MenubarContent>
              </MenubarMenu>
            </Menubar>

            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger>Getting started</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul
                      class="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[minmax(0,.75fr)_minmax(0,1fr)]"
                    >
                      <li class="row-span-3">
                        <NavigationMenuLink as-child>
                          <a
                            class="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                            href="/"
                          >
                            <img src="https://www.reka-ui.com/logo.svg" class="h-6 w-6" />
                            <div class="mb-2 mt-4 text-lg font-medium">shadcn/ui</div>
                            <p class="text-sm leading-tight text-muted-foreground">
                              Beautifully designed components built with Radix UI and Tailwind CSS.
                            </p>
                          </a>
                        </NavigationMenuLink>
                      </li>

                      <li>
                        <NavigationMenuLink as-child>
                          <a
                            href="/docs/introduction"
                            class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          >
                            <div class="text-sm font-medium leading-none">Introduction</div>
                            <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              Re-usable components built using Radix UI and Tailwind CSS.
                            </p>
                          </a>
                        </NavigationMenuLink>
                      </li>
                      <li>
                        <NavigationMenuLink as-child>
                          <a
                            href="/docs/installation"
                            class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          >
                            <div class="text-sm font-medium leading-none">Installation</div>
                            <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              How to install dependencies and structure your app.
                            </p>
                          </a>
                        </NavigationMenuLink>
                      </li>
                      <li>
                        <NavigationMenuLink as-child>
                          <a
                            href="/docs/typography"
                            class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          >
                            <div class="text-sm font-medium leading-none">Typography</div>
                            <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              Styles for headings, paragraphs, lists...etc
                            </p>
                          </a>
                        </NavigationMenuLink>
                      </li>
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuTrigger>Components</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul class="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      <li v-for="component in components" :key="component.title">
                        <NavigationMenuLink as-child>
                          <a
                            :href="component.href"
                            class="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                          >
                            <div class="text-sm font-medium leading-none">
                              {{ component.title }}
                            </div>
                            <p class="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              {{ component.description }}
                            </p>
                          </a>
                        </NavigationMenuLink>
                      </li>
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink href="/docs/introduction"> Documentation </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            <NumberField id="age" :default-value="18" :min="0">
              <Label for="age">Age</Label>
              <NumberFieldContent>
                <NumberFieldDecrement />
                <NumberFieldInput />
                <NumberFieldIncrement />
              </NumberFieldContent>
            </NumberField>

            <Pagination v-slot="{ page }" :items-per-page="10" :total="30" :default-page="2">
              <PaginationContent v-slot="{ items }">
                <PaginationPrevious />

                <template v-for="(item, index) in items" :key="index">
                  <PaginationItem
                    v-if="item.type === 'page'"
                    :value="item.value"
                    :is-active="item.value === page"
                  >
                    {{ item.value }}
                  </PaginationItem>
                </template>

                <PaginationEllipsis :index="4" />

                <PaginationNext />
              </PaginationContent>
            </Pagination>

            <div>
              <PinInput id="pin-input" v-model="value" placeholder="○" @complete="handleComplete">
                <PinInputGroup>
                  <PinInputSlot v-for="(id, index) in 5" :key="id" :index="index" />
                </PinInputGroup>
              </PinInput>
            </div>

            <Popover>
              <PopoverTrigger as-child>
                <Button variant="outline"> Open popover </Button>
              </PopoverTrigger>
              <PopoverContent class="w-80">
                <div class="grid gap-4">
                  <div class="space-y-2">
                    <h4 class="font-medium leading-none">Dimensions</h4>
                    <p class="text-sm text-muted-foreground">Set the dimensions for the layer.</p>
                  </div>
                  <div class="grid gap-2">
                    <div class="grid grid-cols-3 items-center gap-4">
                      <Label for="width">Width</Label>
                      <Input id="width" type="text" default-value="100%" class="col-span-2 h-8" />
                    </div>
                    <div class="grid grid-cols-3 items-center gap-4">
                      <Label for="maxWidth">Max. width</Label>
                      <Input
                        id="maxWidth"
                        type="text"
                        default-value="300px"
                        class="col-span-2 h-8"
                      />
                    </div>
                    <div class="grid grid-cols-3 items-center gap-4">
                      <Label for="height">Height</Label>
                      <Input id="height" type="text" default-value="25px" class="col-span-2 h-8" />
                    </div>
                    <div class="grid grid-cols-3 items-center gap-4">
                      <Label for="maxHeight">Max. height</Label>
                      <Input
                        id="maxHeight"
                        type="text"
                        default-value="none"
                        class="col-span-2 h-8"
                      />
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Progress v-model="progress" class="w-3/5" />

            <ResizablePanelGroup
              id="demo-group-1"
              direction="horizontal"
              class="max-w-md rounded-lg border"
            >
              <ResizablePanel id="demo-panel-1" :default-size="50">
                <div class="flex h-[200px] items-center justify-center p-6">
                  <span class="font-semibold">One</span>
                </div>
              </ResizablePanel>
              <ResizableHandle id="demo-handle-1" />
              <ResizablePanel id="demo-panel-2" :default-size="50">
                <ResizablePanelGroup id="demo-group-2" direction="vertical">
                  <ResizablePanel id="demo-panel-3" :default-size="25">
                    <div class="flex h-full items-center justify-center p-6">
                      <span class="font-semibold">Two</span>
                    </div>
                  </ResizablePanel>
                  <ResizableHandle id="demo-handle-2" />
                  <ResizablePanel id="demo-panel-4" :default-size="75">
                    <div class="flex h-full items-center justify-center p-6">
                      <span class="font-semibold">Three</span>
                    </div>
                  </ResizablePanel>
                </ResizablePanelGroup>
              </ResizablePanel>
            </ResizablePanelGroup>

            <Select>
              <SelectTrigger class="w-[180px]">
                <SelectValue placeholder="Select a fruit" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Fruits</SelectLabel>
                  <SelectItem value="apple"> Apple </SelectItem>
                  <SelectItem value="banana"> Banana </SelectItem>
                  <SelectItem value="blueberry"> Blueberry </SelectItem>
                  <SelectItem value="grapes"> Grapes </SelectItem>
                  <SelectItem value="pineapple"> Pineapple </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>

            <Slider
              v-model="modelValue"
              :max="100"
              :step="1"
              :class="cn('w-3/5', $attrs.class ?? '')"
            />

            <Stepper>
              <StepperItem
                v-for="item in steps"
                :key="item.step"
                class="basis-1/4"
                :step="item.step"
              >
                <StepperTrigger>
                  <StepperIndicator>
                    <!-- <component :is="item.icon" class="w-4 h-4" /> -->
                    <MattIcon :name="item.icon" />
                  </StepperIndicator>
                  <div class="flex flex-col">
                    <StepperTitle>
                      {{ item.title }}
                    </StepperTitle>
                    <StepperDescription>
                      {{ item.description }}
                    </StepperDescription>
                  </div>
                </StepperTrigger>
                <StepperSeparator
                  v-if="item.step !== steps[steps.length - 1].step"
                  class="w-full h-px"
                />
              </StepperItem>
            </Stepper>

            <Switch id="airplane-mode" />

            <Table>
              <TableCaption>A list of your recent invoices.</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead class="w-[100px]"> Invoice </TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead class="text-right"> Amount </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="invoice in invoices" :key="invoice.invoice">
                  <TableCell class="font-medium">
                    {{ invoice.invoice }}
                  </TableCell>
                  <TableCell>{{ invoice.paymentStatus }}</TableCell>
                  <TableCell>{{ invoice.paymentMethod }}</TableCell>
                  <TableCell class="text-right">
                    {{ invoice.totalAmount }}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            <Tabs default-value="account" class="w-[400px]">
              <TabsList class="grid w-full grid-cols-2">
                <TabsTrigger value="account"> Account </TabsTrigger>
                <TabsTrigger value="password"> Password </TabsTrigger>
              </TabsList>
              <TabsContent value="account">
                <Card>
                  <CardHeader>
                    <CardTitle>Account</CardTitle>
                    <CardDescription>
                      Make changes to your account here. Click save when you're done.
                    </CardDescription>
                  </CardHeader>
                  <CardContent class="space-y-2">
                    <div class="space-y-1">
                      <Label for="name">Name</Label>
                      <Input id="name" default-value="Pedro Duarte" />
                    </div>
                    <div class="space-y-1">
                      <Label for="username">Username</Label>
                      <Input id="username" default-value="@peduarte" />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button>Save changes</Button>
                  </CardFooter>
                </Card>
              </TabsContent>
              <TabsContent value="password">
                <Card>
                  <CardHeader>
                    <CardTitle>Password</CardTitle>
                    <CardDescription>
                      Change your password here. After saving, you'll be logged out.
                    </CardDescription>
                  </CardHeader>
                  <CardContent class="space-y-2">
                    <div class="space-y-1">
                      <Label for="current">Current password</Label>
                      <Input id="current" type="password" />
                    </div>
                    <div class="space-y-1">
                      <Label for="new">New password</Label>
                      <Input id="new" type="password" />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button>Save password</Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </Tabs>

            <TagsInput v-model="tagValue">
              <TagsInputItem v-for="item in tagValue" :key="item" :value="item">
                <TagsInputItemText />
                <TagsInputItemDelete />
              </TagsInputItem>

              <TagsInputInput placeholder="Fruits..." />
            </TagsInput>
            <Textarea placeholder="Type your message here." />

            <Toggle aria-label="Toggle italic">
              <MattIcon name="Bold" class="h-4 w-4" />
            </Toggle>

            <ToggleGroup type="multiple">
              <ToggleGroupItem value="bold" aria-label="Toggle bold">
                <MattIcon name="Bold" class="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="italic" aria-label="Toggle italic">
                <MattIcon name="Italic" class="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="underline" aria-label="Toggle underline">
                <MattIcon name="Underline" class="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
        </div>
      </div>
    </ScrollArea>
  </div>
</template>

<script setup lang="ts">
import email from '@mattverse/shared/assets/svg/email.svg'
import { MattIcon } from '@mattverse/mattverse-ui'
import { serverService } from '@mattverse/shared'

const message = ref('Hello Electron')
const isVisible = ref(true)
const isOpen = ref(false)
const value = ref<string[]>([])
const modelValue = ref([50])
const tagValue = ref(['Apple', 'Banana'])
const progress = ref(13)
const position = ref('top')
const handleComplete = (e: string[]) => alert(e.join(''))
const defaultValue = 'item-1'

// HighPower Electron API 测试相关
const appInfo = ref({
  appName: '',
  version: '',
  features: [] as string[],
})

const systemInfo = ref({
  platform: '',
  nodeVersion: '',
  electronVersion: '',
})

const testResult = ref('')
let currentJobId = ''

// gRPC 相关状态
const grpcStatus = ref({
  initialized: false,
  connected: false,
  url: null as string | null,
  userId: null as string | null,
})

const grpcTestResult = ref('')
const grpcLoading = ref(false)

// 初始化时获取基本信息
onMounted(async () => {
  try {
    // 获取应用配置
    const config = await window.electronAPI.getConfig()
    appInfo.value = config

    // 获取系统信息
    systemInfo.value = {
      platform: window.electronAPI.platform,
      nodeVersion: window.electronAPI.versions?.node || 'unknown',
      electronVersion: window.electronAPI.versions?.electron || 'unknown',
    }

    // 获取 gRPC 初始状态
    await refreshGrpcStatus()
  } catch (error) {
    window.logger?.info('Failed to load initial data:', error)
    testResult.value = `初始化错误: ${error}`
  }
})

// HighPower 特定的 API 测试方法
const testStartJob = async () => {
  try {
    const jobConfig = {
      type: 'matrix-multiplication',
      size: 1000,
      iterations: 10,
    }
    const result = await window.electronAPI.compute.startJob(jobConfig)
    currentJobId = result.jobId
    testResult.value = `计算任务已启动:\n${JSON.stringify(result, null, 2)}`
  } catch (error) {
    testResult.value = `启动计算任务失败: ${error}`
  }
}

const testGetJobStatus = async () => {
  try {
    if (!currentJobId) {
      testResult.value = '请先启动一个计算任务'
      return
    }
    const status = await window.electronAPI.compute.getStatus(currentJobId)
    testResult.value = `任务状态:\n${JSON.stringify(status, null, 2)}`
  } catch (error) {
    testResult.value = `获取任务状态失败: ${error}`
  }
}

const testGPUInfo = async () => {
  try {
    const gpuInfo = await window.electronAPI.highpower.getGPUInfo()
    testResult.value = `GPU信息:\n${JSON.stringify(gpuInfo, null, 2)}`
  } catch (error) {
    testResult.value = `获取GPU信息失败: ${error}`
  }
}

const invoices = [
  {
    invoice: 'INV001',
    paymentStatus: 'Paid',
    totalAmount: '$250.00',
    paymentMethod: 'Credit Card',
  },
  {
    invoice: 'INV002',
    paymentStatus: 'Pending',
    totalAmount: '$150.00',
    paymentMethod: 'PayPal',
  },
  {
    invoice: 'INV003',
    paymentStatus: 'Unpaid',
    totalAmount: '$350.00',
    paymentMethod: 'Bank Transfer',
  },
  {
    invoice: 'INV004',
    paymentStatus: 'Paid',
    totalAmount: '$450.00',
    paymentMethod: 'Credit Card',
  },
  {
    invoice: 'INV005',
    paymentStatus: 'Paid',
    totalAmount: '$550.00',
    paymentMethod: 'PayPal',
  },
  {
    invoice: 'INV006',
    paymentStatus: 'Pending',
    totalAmount: '$200.00',
    paymentMethod: 'Bank Transfer',
  },
  {
    invoice: 'INV007',
    paymentStatus: 'Unpaid',
    totalAmount: '$300.00',
    paymentMethod: 'Credit Card',
  },
]
const steps = [
  {
    step: 1,
    title: 'Address',
    description: 'Add your address here',
    icon: 'BookUser',
  },
  {
    step: 2,
    title: 'Shipping',
    description: 'Set your preferred shipping method',
    icon: 'Truck',
  },
  {
    step: 3,
    title: 'Payment',
    description: 'Add any payment information you have',
    icon: 'CreditCard',
  },
  {
    step: 4,
    title: 'Checkout',
    description: 'Confirm your order',
    icon: 'Check',
  },
]
const accordionItems = [
  {
    value: 'item-1',
    title: 'Is it accessible?',
    content: 'Yes. It adheres to the WAI-ARIA design pattern.',
  },
  {
    value: 'item-2',
    title: 'Is it unstyled?',
    content: "Yes. It's unstyled by default, giving you freedom over the look and feel.",
  },
  {
    value: 'item-3',
    title: 'Can it be animated?',
    content: 'Yes! You can use the transition prop to configure the animation.',
  },
]
const frameworks = [
  { value: 'next.js', label: 'Next.js' },
  { value: 'sveltekit', label: 'SvelteKit' },
  { value: 'nuxt', label: 'Nuxt' },
  { value: 'remix', label: 'Remix' },
  { value: 'astro', label: 'Astro' },
]

const components: { title: string; href: string; description: string }[] = [
  {
    title: 'Alert Dialog',
    href: '/docs/components/alert-dialog',
    description:
      'A modal dialog that interrupts the user with important content and expects a response.',
  },
  {
    title: 'Hover Card',
    href: '/docs/components/hover-card',
    description: 'For sighted users to preview content available behind a link.',
  },
  {
    title: 'Progress',
    href: '/docs/components/progress',
    description:
      'Displays an indicator showing the completion progress of a task, typically displayed as a progress bar.',
  },
  {
    title: 'Scroll-area',
    href: '/docs/components/scroll-area',
    description: 'Visually or semantically separates content.',
  },
  {
    title: 'Tabs',
    href: '/docs/components/tabs',
    description:
      'A set of layered sections of content—known as tab panels—that are displayed one at a time.',
  },
  {
    title: 'Tooltip',
    href: '/docs/components/tooltip',
    description:
      'A popup that displays information related to an element when the element receives keyboard focus or the mouse hovers over it.',
  },
]

// gRPC 相关方法
const refreshGrpcStatus = async () => {
  try {
    const status = await window.electronAPI.invoke('grpc:get-status')
    grpcStatus.value = {
      initialized: status.initialized || false,
      connected: status.connected || false,
      url: status.url || null,
      userId: status.userId || null,
    }
  } catch (error) {
    window.logger?.error('获取 gRPC 状态失败:', error)
    grpcStatus.value = {
      initialized: false,
      connected: false,
      url: null,
      userId: null,
    }
  }
}

const initGrpc = async () => {
  try {
    grpcLoading.value = true
    const result = await window.electronAPI.invoke('grpc:init')

    if (result.success) {
      grpcTestResult.value = `gRPC 初始化成功:\n${JSON.stringify(result, null, 2)}`
      await refreshGrpcStatus()
    } else {
      grpcTestResult.value = `gRPC 初始化失败:\n${result.error || '未知错误'}`
    }
  } catch (error) {
    grpcTestResult.value = `gRPC 初始化异常:\n${error instanceof Error ? error.message : '未知错误'}`
  } finally {
    grpcLoading.value = false
  }
}

const testGrpcPing = async () => {
  try {
    const result = await window.electronAPI.invoke('grpc:ping')

    if (result.success) {
      grpcTestResult.value = `Ping 测试成功:\n${JSON.stringify(result.data, null, 2)}`
    } else {
      grpcTestResult.value = `Ping 测试失败:\n${result.error || '未知错误'}`
    }
  } catch (error) {
    grpcTestResult.value = `Ping 测试异常:\n${error instanceof Error ? error.message : '未知错误'}`
  }
}

const testGrpcComputeTask = async () => {
  try {
    // 使用 shared 包中的 serverService.getServerList 方法
    const params = {
      user_id: '0',
      token: 'abcdefghijklmn',
      id: '',
    }

    const result = await serverService.getServerList(params)
    grpcTestResult.value = `通用调用测试成功:\n${JSON.stringify(result, null, 2)}`
  } catch (error) {
    grpcTestResult.value = `通用调用测试异常:\n${error instanceof Error ? error.message : '未知错误'}`
  }
}
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
</style>
