/**
 * HighPower 预加载脚本
 */
import { contextBridge, ipcRenderer } from 'electron'
import { createPreloadBridge, logger } from '@mattverse/electron-core'

// 创建 HighPower 特定的自定义 API
const customAPI = {
  // HighPower 特定配置
  getConfig: () => ipcRenderer.invoke('highpower:get-config'),

  // 系统信息
  platform: process.platform,
  versions: process.versions,

  // 高性能计算相关 API
  compute: {
    startJob: (jobConfig: any) => ipcRenderer.invoke('compute:start-job', jobConfig),
    getStatus: (jobId: string) => ipcRenderer.invoke('compute:get-status', jobId),
    cancelJob: (jobId: string) => ipcRenderer.invoke('compute:cancel-job', jobId),
    getJobHistory: () => ipcRenderer.invoke('compute:get-job-history')
  },

  // HighPower 特定功能
  highpower: {
    getGPUInfo: () => ipcRenderer.invoke('highpower:get-gpu-info'),
    getSystemResources: () => ipcRenderer.invoke('highpower:get-system-resources'),
    optimizePerformance: (settings: any) => ipcRenderer.invoke('highpower:optimize-performance', settings)
  }
}

// 使用 electron-core 的预加载桥接工厂
const api = createPreloadBridge(customAPI)

// 暴露 API 到渲染进程
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', api)
    logger.info('HighPower APIs exposed successfully')
  } catch (error) {
    logger.error('Failed to expose HighPower APIs:', error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electronAPI = api
}

// 导出类型定义
export type ElectronAPI = typeof api
