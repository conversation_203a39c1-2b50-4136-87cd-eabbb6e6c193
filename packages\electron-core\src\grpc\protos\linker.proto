syntax = "proto3";
package linker;

import "matt.proto";
import "db.proto";
import "common.proto";

message RegisterRequest {
  string user_id = 1;
  string token = 2;
  string server_name = 3;
  string url = 4;
  string region = 5;
  string version = 6;
  int32 access_level = 7;
  string protocol_type = 8;
  bool is_force_to_register = 9;
  repeated string service_name_list = 10;
  repeated string service_version_list = 11;
  repeated int32 service_access_level_list = 12;
  string server_type = 13;
}

message RegisterResponse {
  common.ResponseStatus status = 1;
  string message = 2;
  string server_id = 3;
}

message TaskResultResponse {
  common.ResponseStatus status = 1;
  string message = 2;
  string result = 3;
}

message FileChunk {
  string file_name = 1;
  string user_id = 2;
  string total_sha256 = 3;
  int64 chunk_id = 4;
  bytes content = 5;
  bool is_last = 6;
}

message UploadResponse {
  common.ResponseStatus status = 1;
  string message = 2;
  string upload_id = 3;
  string stored_filepath = 4;
  int64 total_chunks = 5;
  int64 total_bytes = 6;
}

message DownloadRequest {
  string user_id = 1;
  string token = 2;
  string stored_filepath = 3;
  int64 chunk_size = 4;
}

service LinkerService {
    rpc defaultService (matt.GeneralRequest) returns (matt.GeneralResponse);
    rpc serverStreamService (matt.GeneralRequest) returns (stream matt.GeneralResponse);
    rpc submitService (matt.GeneralRequest) returns (matt.SubmitResponse);
    rpc register (RegisterRequest) returns (RegisterResponse);
    rpc database (db.DbRequest) returns (db.DbResponse);
    rpc ping(common.PingRequest) returns (common.PingResponse);
    rpc getServerList(common.GetRequest) returns (db.DbResponse);
    rpc getTaskList(common.GetRequest) returns (db.DbResponse);
    rpc getTaskResult(common.GetRequest) returns (TaskResultResponse);
    rpc updateTaskProcess(db.DbRequest) returns (db.DbResponse);
    rpc deleteTask(common.OperateRequest) returns (common.OperateResponse);
    rpc stopTask(common.OperateRequest) returns (common.OperateResponse);
    rpc pauseTask(common.OperateRequest) returns (common.OperateResponse);
    rpc resumeTask(common.OperateRequest) returns (common.OperateResponse);
    rpc deleteServerInfo(common.OperateRequest) returns (common.OperateResponse);
    rpc getServerUsage(common.GetRequest) returns (common.ServerOperateResponse);
    rpc getVersion(common.PingRequest) returns (common.GetResponse);
    rpc agentService (matt.AgentRequest) returns (stream matt.AgentResponse);
    rpc addSession (db.SessionRequest) returns (common.OperateResponse);
    rpc deleteSession (common.OperateRequest) returns (common.OperateResponse);
    rpc getSession (common.GetRequest) returns (db.DbResponse);
    rpc appendSessionMessage (db.SessionAppendRequest) returns (common.OperateResponse);
    rpc getUserAllSessions (common.GetRequest) returns (db.DbResponse);
    rpc getUserToken(db.UserRequest) returns (db.DbResponse);
    rpc userSignIn(db.UserRequest) returns (db.DbResponse);
    rpc userSignOut(common.OperateRequest) returns (common.OperateResponse);
    rpc getClientUrl(common.PingRequest) returns (common.GetClientUrlResponse);
    rpc setClientChannel(db.UserRequest) returns (stream matt.ClientApiRequest);
    rpc callClientApi(matt.ClientApiRequest) returns (matt.SubmitResponse);
    rpc getSessionCommand (db.SessionRequest) returns(db.DbResponse);
    rpc updateSessionCommand (db.SessionCommand) returns(db.DbResponse);
    rpc uploadFile(stream FileChunk) returns (UploadResponse);
    rpc downloadFile(DownloadRequest) returns (stream FileChunk);
}
