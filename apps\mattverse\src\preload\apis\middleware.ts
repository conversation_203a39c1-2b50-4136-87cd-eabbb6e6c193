/**
 * 中台配置相关 API
 */
import { ipc<PERSON>enderer } from 'electron'

/**
 * 中台配置 API
 */
export const middlewareAPI = {
  getConfig: () => ipcRenderer.invoke('middleware:get-config'),
  updateConfig: (host: string, port: number) => ipcRenderer.invoke('middleware:update-config', host, port),
  getVersion: () => ipcRenderer.invoke('middleware:get-version'),
  testConnection: (host?: string, port?: number) => ipcRenderer.invoke('middleware:test-connection', host, port),
  restartApp: () => ipcRenderer.invoke('middleware:restart-app'),
}
