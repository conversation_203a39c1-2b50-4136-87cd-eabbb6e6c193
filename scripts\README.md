# Scripts 目录说明

本目录包含了 MattVerse Monorepo 项目的各种脚本工具，按功能分类组织，用于简化日常开发、构建、维护等操作。

## 目录结构

```
scripts/
├── build/          # 构建相关脚本
├── dev/            # 开发相关脚本
├── maintenance/    # 维护和管理脚本
├── utils/          # 通用工具脚本
└── README.md       # 本说明文档
```

## 🏗️ build/ - 构建脚本

### build-all-platforms.js

跨平台构建脚本，支持并发构建多个应用的多个平台版本。

**使用方法：**
```bash
# 构建所有应用的所有平台
node scripts/build/build-all-platforms.js

# 构建特定应用
node scripts/build/build-all-platforms.js mattverse

# 构建特定平台
node scripts/build/build-all-platforms.js --platforms win mac

# 交互式选择（推荐）
node scripts/build/build-all-platforms.js
```

**特性：**
- 并发控制构建，提高效率
- 实时进度监控和状态显示
- 构建结果统计和错误处理
- 交互式选择模式

### build-all.sh

Linux/macOS 批量构建脚本，支持交互式选择。

**使用方法：**
```bash
# 交互式选择（推荐）
./scripts/build/build-all.sh

# 构建特定平台和应用
./scripts/build/build-all.sh mac mattverse
```

### build-all.ps1

Windows 批量构建脚本，支持交互式选择。

**使用方法：**
```powershell
# 交互式选择（推荐）
.\scripts\build\build-all.ps1

# 构建特定平台和应用
.\scripts\build\build-all.ps1 -Platform win -Apps @("mattverse")
```

## ⚡ dev/ - 开发脚本

### dev-manager.js

交互式开发环境管理器，支持多应用开发服务器的统一管理。

**使用方法：**
```bash
pnpm dev:manager
```

**支持的命令：**
- `start <app>` - 启动指定应用 (mattverse, highpower, ui, flow)
- `stop <app>` - 停止指定应用
- `restart <app>` - 重启指定应用
- `start-all` - 启动所有应用
- `stop-all` - 停止所有应用
- `status` - 显示状态
- `menu` - 显示交互菜单
- `help` - 显示帮助
- `quit/exit` - 退出管理器

**支持的应用：**
- `mattverse` - MattVerse 主应用
- `highpower` - HighPower 应用
- `ui` - UI 组件库
- `flow` - Flow 组件库

**特性：**
- 彩色输出和状态显示
- 进程生命周期管理
- 实时运行状态监控
- 交互式菜单模式

## 🔧 maintenance/ - 维护脚本

### health-check.js

项目健康检查工具，全面检查项目配置和状态。

**使用方法：**
```bash
pnpm health-check
```

**检查项目：**
- package.json 配置完整性
- 工作空间结构正确性
- 配置文件存在性
- 依赖安装状态
- 构建产物完整性
- Git 工作目录状态

### clean-all.ps1

Windows 批量清理脚本，支持多种清理类型和交互式选择。

**使用方法：**
```powershell
# 交互式选择（推荐）
.\scripts\maintenance\clean-all.ps1

# 清理特定类型
.\scripts\maintenance\clean-all.ps1 -Type build
.\scripts\maintenance\clean-all.ps1 -Type deps
.\scripts\maintenance\clean-all.ps1 -Type cache
.\scripts\maintenance\clean-all.ps1 -Type all
```

### clean-all.sh

Linux/macOS 批量清理脚本，功能与 Windows 版本相同。

**使用方法：**
```bash
# 交互式选择（推荐）
./scripts/maintenance/clean-all.sh

# 清理特定类型
./scripts/maintenance/clean-all.sh build
./scripts/maintenance/clean-all.sh deps
./scripts/maintenance/clean-all.sh cache
./scripts/maintenance/clean-all.sh all
```

**清理类型说明：**
- `build` - 清理构建产物（dist, out, release）
- `deps` - 清理依赖文件（node_modules, pnpm-lock.yaml）
- `cache` - 清理缓存文件（.turbo, .eslintcache）
- `all` - 清理所有类型

## 🛠️ utils/ - 工具脚本

### project-info.js
项目信息查看工具，显示项目结构、依赖关系、构建状态等。

**使用方法：**
```bash
# 显示项目概览（默认）
node scripts/utils/project-info.js

# 显示应用信息
node scripts/utils/project-info.js apps

# 显示包信息
node scripts/utils/project-info.js packages

# 显示可用脚本
node scripts/utils/project-info.js scripts

# 显示所有信息
node scripts/utils/project-info.js all
```

**功能特性：**
- 项目概览统计
- 应用详细信息
- 包依赖分析
- 脚本分类展示
- 构建状态检查

### interactive-launcher.js

交互式脚本启动器，提供统一的脚本选择和执行界面。

**使用方法：**
```bash
pnpm scripts
```

**功能特性：**
- 按分类显示可用脚本
- 交互式选择和执行
- 跨平台脚本自动检测
- 脚本执行确认和结果展示
- 支持交互式脚本（如 dev-manager）

## 🚀 快速开始

### 统一入口（推荐）
```bash
# 交互式脚本启动器 - 一站式脚本管理
pnpm scripts
```

通过交互式界面选择需要的脚本分类和具体脚本，适合不熟悉命令行的用户。

### 项目初始化
```bash
# 完整项目设置
pnpm setup

# 或手动步骤
pnpm install
pnpm build:packages
pnpm health-check
```

### 日常开发
```bash
# 启动开发管理器（推荐）
pnpm dev:manager

# 查看项目信息
pnpm project-info

# 健康检查
pnpm health-check
```

### 构建发布
```bash
# 交互式构建（推荐）
pnpm scripts
# 选择：构建脚本 → 跨平台构建

# 或直接使用命令
pnpm build:all-platforms
```

### 项目维护
```bash
# 交互式清理（推荐）
pnpm scripts
# 选择：维护工具 → 批量清理

# 或直接使用命令
pnpm health-check
pnpm project-info all
```

## 📝 脚本开发规范

### 命名规范
- 使用 kebab-case 命名文件
- 脚本文件使用 `.js` 或 `.ps1`/`.sh` 扩展名
- 功能相关的脚本放在同一目录下

### 代码规范
- 所有脚本都应包含详细的注释和使用说明
- 使用颜色输出提升用户体验
- 提供错误处理和异常捕获
- 支持命令行参数和选项

### 文档规范
- 每个脚本都应在本 README 中有对应说明
- 包含使用方法、参数说明、功能特性
- 提供实际的使用示例

### 新增脚本流程
1. 确定脚本功能分类，选择合适的目录
2. 编写脚本，添加详细注释和错误处理
3. 在 package.json 中添加快捷命令
4. 更新本 README 文档
5. 测试脚本在不同环境下的运行情况

## ⚠️ 使用注意事项

### 环境要求
- **Node.js**: 18.0.0 或更高版本
- **pnpm**: 9.0.0 或更高版本
- **PowerShell**: Windows 用户需要 PowerShell 5.1 或更高版本

### 权限问题
- Windows 用户可能需要设置 PowerShell 执行策略
- 某些清理脚本可能需要管理员权限
- 确保在项目根目录运行所有脚本

### 平台兼容性
- `.ps1` 脚本仅适用于 Windows
- `.sh` 脚本适用于 Linux/macOS
- `.js` 脚本跨平台兼容

## 🔗 相关链接

- [项目根目录 package.json](../package.json) - 查看所有可用脚本
- [Turbo 配置](../turbo.json) - 构建配置
- [项目文档](../docs/) - 详细文档
- [Monorepo 架构说明](../docs/specs/monorepo-optimization/) - 项目架构文档
