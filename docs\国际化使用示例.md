# 国际化使用示例

## 📁 优化后的目录结构

```
packages/i18n/
├── src/
│   ├── index.ts                    # 主入口，导出所有 API
│   ├── types.ts                    # TypeScript 类型定义
│   └── locales/                    # 语言包目录
│       ├── zh-CN/                  # 中文语言包
│       │   ├── index.ts            # 中文主入口
│       │   ├── common.ts           # 通用词汇（确认、取消、保存等）
│       │   ├── navigation.ts       # 导航相关（菜单项）
│       │   ├── components.ts       # 组件相关（按组件分类）
│       │   ├── messages.ts         # 系统消息（成功、失败、错误等）
│       │   ├── workflow.ts         # 工作流相关
│       │   └── settings.ts         # 设置相关
│       └── en-US/                  # 英文语言包（结构相同）
│           ├── index.ts
│           ├── common.ts
│           ├── navigation.ts
│           ├── components.ts
│           ├── messages.ts
│           ├── workflow.ts
│           └── settings.ts
```

## 🎯 设计优势

### 1. **模块化分类**
- 按功能模块分类，便于维护
- 每个文件职责单一，易于查找
- 支持大量组件的翻译管理

### 2. **类型安全**
- 完整的 TypeScript 类型定义
- 编译时检查翻译键的正确性
- 自动补全和错误提示

### 3. **扩展性强**
- 易于添加新的翻译分类
- 支持嵌套结构
- 便于团队协作

## 🚀 使用示例

### 1. 基础使用

```vue
<template>
  <div>
    <!-- 通用词汇 -->
    <button>{{ $t('common.save') }}</button>
    <button>{{ $t('common.cancel') }}</button>
    
    <!-- 导航相关 -->
    <h1>{{ $t('navigation.workflow') }}</h1>
    
    <!-- 组件相关 -->
    <div>{{ $t('components.sidebar.toggle') }}</div>
    
    <!-- 系统消息 -->
    <div class="success">{{ $t('messages.save_success') }}</div>
  </div>
</template>

<script setup lang="ts">
// 自动导入，无需手动 import
const { t, locale } = useI18n()

// 切换语言
const switchLanguage = () => {
  locale.value = locale.value === 'zh-CN' ? 'en-US' : 'zh-CN'
}
</script>
```

### 2. 组件中的复杂使用

```vue
<template>
  <div class="workflow-panel">
    <!-- 工作流标题 -->
    <h2>{{ $t('workflow.title') }}</h2>
    
    <!-- 操作按钮 -->
    <div class="actions">
      <button @click="createWorkflow">
        {{ $t('workflow.create') }}
      </button>
      <button @click="runWorkflow" :disabled="!selectedWorkflow">
        {{ $t('workflow.run') }}
      </button>
    </div>
    
    <!-- 状态显示 -->
    <div class="status">
      <span>{{ $t('workflow.status.running') }}</span>
    </div>
    
    <!-- 表格组件 -->
    <div v-if="workflows.length === 0">
      {{ $t('components.table.no_data') }}
    </div>
    
    <!-- 带参数的翻译 -->
    <div class="pagination">
      {{ $t('components.table.total_items', { count: totalCount }) }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const workflows = ref([])
const selectedWorkflow = ref(null)
const totalCount = ref(0)

const createWorkflow = () => {
  // 创建工作流逻辑
  console.log(t('workflow.create'))
}

const runWorkflow = () => {
  // 运行工作流逻辑
  console.log(t('workflow.run'))
}
</script>
```

### 3. 表单验证示例

```vue
<template>
  <form @submit="handleSubmit">
    <div class="form-field">
      <label>{{ $t('workflow.properties.name') }}</label>
      <input 
        v-model="form.name" 
        :placeholder="$t('workflow.properties.name')"
        :class="{ error: errors.name }"
      />
      <span v-if="errors.name" class="error-message">
        {{ $t('components.form.required') }}
      </span>
    </div>
    
    <div class="form-field">
      <label>{{ $t('workflow.properties.description') }}</label>
      <textarea 
        v-model="form.description"
        :placeholder="$t('workflow.properties.description')"
      />
    </div>
    
    <div class="form-actions">
      <button type="submit">{{ $t('common.save') }}</button>
      <button type="button" @click="resetForm">{{ $t('common.reset') }}</button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'

const form = reactive({
  name: '',
  description: ''
})

const errors = ref({})

const handleSubmit = () => {
  // 验证逻辑
  if (!form.name) {
    errors.value.name = true
    return
  }
  
  // 提交成功
  console.log(t('messages.save_success'))
}

const resetForm = () => {
  Object.assign(form, { name: '', description: '' })
  errors.value = {}
}
</script>
```

### 4. 语言切换组件

```vue
<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="ghost" size="sm">
        <span>{{ currentLocaleInfo.flag }}</span>
        <span class="ml-2">{{ currentLocaleInfo.name }}</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent>
      <DropdownMenuLabel>
        {{ $t('components.language_selector.title') }}
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuItem
        v-for="locale in SUPPORTED_LOCALES"
        :key="locale.code"
        @click="switchLanguage(locale.code)"
        :class="{ 'bg-accent': locale.code === currentLocale }"
      >
        <span class="mr-2">{{ locale.flag }}</span>
        <span>{{ locale.name }}</span>
        <Check v-if="locale.code === currentLocale" class="ml-auto h-4 w-4" />
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Check } from 'lucide-vue-next'
import { SUPPORTED_LOCALES, type SupportedLocale } from '@mattverse/i18n'

const { locale } = useI18n()

const currentLocale = computed(() => locale.value as SupportedLocale)

const currentLocaleInfo = computed(() => {
  return SUPPORTED_LOCALES.find(l => l.code === currentLocale.value) || SUPPORTED_LOCALES[0]
})

const switchLanguage = (newLocale: SupportedLocale) => {
  locale.value = newLocale
  // 可选：保存到 localStorage
  localStorage.setItem('preferred-language', newLocale)
}
</script>
```

## 📝 添加新翻译的步骤

### 1. 添加新的组件翻译

```typescript
// packages/i18n/src/locales/zh-CN/components.ts
export default {
  // 现有组件...
  
  // 新增组件翻译
  data_table: {
    search_placeholder: '搜索数据...',
    filter_button: '筛选',
    export_button: '导出数据',
    columns: {
      name: '名称',
      status: '状态',
      created_at: '创建时间',
      actions: '操作'
    }
  }
}

// packages/i18n/src/locales/en-US/components.ts
export default {
  // 现有组件...
  
  // 新增组件翻译
  data_table: {
    search_placeholder: 'Search data...',
    filter_button: 'Filter',
    export_button: 'Export Data',
    columns: {
      name: 'Name',
      status: 'Status',
      created_at: 'Created At',
      actions: 'Actions'
    }
  }
}
```

### 2. 更新类型定义

```typescript
// packages/i18n/src/types.ts
export interface MessageSchema {
  components: {
    // 现有组件类型...
    
    data_table: {
      search_placeholder: string
      filter_button: string
      export_button: string
      columns: {
        name: string
        status: string
        created_at: string
        actions: string
      }
    }
  }
  // 其他分类...
}
```

### 3. 在组件中使用

```vue
<template>
  <div class="data-table">
    <div class="table-header">
      <input 
        :placeholder="$t('components.data_table.search_placeholder')"
        v-model="searchQuery"
      />
      <button>{{ $t('components.data_table.filter_button') }}</button>
      <button>{{ $t('components.data_table.export_button') }}</button>
    </div>
    
    <table>
      <thead>
        <tr>
          <th>{{ $t('components.data_table.columns.name') }}</th>
          <th>{{ $t('components.data_table.columns.status') }}</th>
          <th>{{ $t('components.data_table.columns.created_at') }}</th>
          <th>{{ $t('components.data_table.columns.actions') }}</th>
        </tr>
      </thead>
      <!-- 表格内容 -->
    </table>
  </div>
</template>
```

## 🔧 构建和测试

```bash
# 构建 i18n 包
pnpm build:i18n

# 在应用中测试
pnpm dev:mattverse

# 类型检查
pnpm typecheck
```

这个优化后的结构更适合管理大量组件的翻译，提供了更好的组织性和可维护性。
