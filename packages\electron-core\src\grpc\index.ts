/**
 * gRPC 模块主入口
 */
import { LinkerGrpcClient } from './rpcMgr'
import type { GrpcClientOptions } from './rpcMgr'

// 导出客户端
export { LinkerGrpcClient } from './rpcMgr'
export type { GrpcClientOptions, CallOptions } from './rpcMgr'

// 导出类型定义
export type * from '../types/grpc'

// 创建客户端的便捷函数
export function createLinkerClient(
  options: GrpcClientOptions,
  userId: string,
  token: string
): LinkerGrpcClient {
  return new LinkerGrpcClient(options, userId, token)
}
