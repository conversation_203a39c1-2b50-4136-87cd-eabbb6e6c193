<template>
  <div>
    <Sidebar v-bind="props">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground group-data-[collapsible=icon]:!justify-center"
            >
              <div
                class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground group-data-[collapsible=icon]:size-full group-data-[collapsible=icon]:rounded-md group-data-[collapsible=icon]:bg-transparent"
              >
                <img
                  :src="mattverseIcon"
                  alt="mattverse"
                  class="group-data-[collapsible=icon]:size-6"
                />
              </div>
              <div
                class="grid flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden"
              >
                <span class="truncate font-semibold">Mattverse</span>
                <span class="truncate text-xs">{{ appInfo.version }}</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
        <SearchForm class="group-data-[collapsible=icon]:hidden" />
      </SidebarHeader>
      <SidebarContent class="scrollbar-thin">
        <NavMain :items="navMainItems" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser :user="data.user" />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  </div>
</template>

<script setup lang="ts">
import type { SidebarProps } from '@mattverse/mattverse-ui'
import NavMain from './NavMain.vue'
import NavUser from './NavUser.vue'
import SearchForm from './SearchForm.vue'
import { useRouter } from 'vue-router'
import { provide } from 'vue'
import mattverseIcon from '@mattverse/shared/assets/images/icons/mattverse/mattverse.ico'

const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: 'icon',
  side: 'left',
  variant: 'floating',
})

const router = useRouter()
const appInfo = ref<{ appName: string; version: string; features: string[] }>({
  appName: '',
  version: '',
  features: [],
})

// 从路由配置生成导航数据
const navMainItems = computed(() => {
  const mainRoute = router.getRoutes().find((route) => route.path === '/')
  if (!mainRoute?.children) return []

  return mainRoute.children
    .filter((route) => route.meta?.title && route.meta?.icon && route.meta?.showInMenu !== false)
    .sort((a, b) => ((a.meta?.sort as number) || 0) - ((b.meta?.sort as number) || 0))
    .map((route) => ({
      title: route.meta?.title as string,
      url: route.path,
      icon: route.meta?.icon as string,
      isActive: route.meta?.isActive as boolean,
      items: route.children?.length
        ? route.children
            .filter((child) => child.meta?.title && child.meta?.icon)
            .sort((a, b) => ((a.meta?.sort as number) || 0) - ((b.meta?.sort as number) || 0))
            .map((child) => ({
              title: child.meta?.title as string,
              url: child.path,
              icon: child.meta?.icon as string,
              meta: {
                showInMenu: child.meta?.showInMenu,
              },
            }))
        : undefined,
    }))
})

// 向子组件提供导航数据，供搜索功能使用
provide('navItems', navMainItems)

const data = {
  user: {
    name: 'Mattverse',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
}

onMounted(async () => {
  const config = await (window as any).electronAPI.getConfig()
  appInfo.value = config
})
</script>

<style scoped></style>
