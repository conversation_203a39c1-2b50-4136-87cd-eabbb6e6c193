<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="max-w-6xl max-h-[90vh] w-[90vw]">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <MattIcon name="FileText" class="h-5 w-5" />
          任务日志详情
        </DialogTitle>
        <DialogDescription> 查看任务的详细日志信息、执行结果和相关数据 </DialogDescription>
      </DialogHeader>

      <div class="dialog-body flex flex-col gap-6 p-4">
        <div v-if="taskData" class="space-y-6">
          <!-- 基本信息卡片 -->
          <Card class="border-border/50">
            <CardHeader class="pb-3">
              <CardTitle class="text-base flex items-center gap-2">
                <MattIcon name="Info" class="h-4 w-4" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- 任务ID -->
                <div class="space-y-2">
                  <Label class="text-sm font-medium text-muted-foreground">任务ID</Label>
                  <div class="flex items-center gap-2">
                    <div class="p-2 bg-muted rounded font-mono text-sm flex-1 break-all">
                      {{ taskData.taskId }}
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      class="h-8 w-8 flex-shrink-0"
                      @click="copyToClipboard(taskData.taskId)"
                      title="复制任务ID"
                    >
                      <MattIcon name="Copy" class="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <!-- 服务名称 -->
                <div class="space-y-2">
                  <Label class="text-sm font-medium text-muted-foreground">服务名称</Label>
                  <div class="p-2 bg-muted rounded text-sm">
                    {{ getServiceName(taskData.taskId) }}
                  </div>
                </div>

                <!-- 运行状态 -->
                <div class="space-y-2">
                  <Label class="text-sm font-medium text-muted-foreground">运行状态</Label>
                  <div class="flex items-center">
                    <Badge :class="getStatusClass(taskData.taskStatus)">
                      {{ getStatusText(taskData.taskStatus) }}
                    </Badge>
                  </div>
                </div>

                <!-- 进度 -->
                <div class="space-y-2">
                  <Label class="text-sm font-medium text-muted-foreground">执行进度</Label>
                  <div class="flex items-center gap-2">
                    <Progress
                      :model-value="getValidProgress(taskData.taskProcess)"
                      class="flex-1"
                    />
                    <span class="text-sm font-medium min-w-[50px]">
                      {{ (taskData.taskProcess || 0).toFixed(1) }}%
                    </span>
                  </div>
                </div>

                <!-- 提交时间 -->
                <div class="space-y-2">
                  <Label class="text-sm font-medium text-muted-foreground">提交时间</Label>
                  <div class="p-2 bg-muted rounded text-sm">
                    {{ formatTimestamp(taskData.createTime) }}
                  </div>
                </div>

                <!-- 运行时间 -->
                <div class="space-y-2">
                  <Label class="text-sm font-medium text-muted-foreground">运行时间</Label>
                  <div class="p-2 bg-muted rounded text-sm">
                    {{ taskData.duration || '未开始' }}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 执行结果卡片 -->
          <Card v-if="taskData.result" class="border-border/50">
            <CardHeader class="pb-3">
              <CardTitle class="text-base flex items-center gap-2">
                <MattIcon name="Terminal" class="h-4 w-4" />
                执行结果
                <div class="ml-auto flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    @click="copyToClipboard(taskData.result)"
                    class="h-8"
                  >
                    <MattIcon name="Copy" class="h-3 w-3 mr-1" />
                    复制
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    @click="openJsonViewer"
                    class="h-8"
                    :disabled="!isValidJson(taskData.result)"
                  >
                    <MattIcon name="Eye" class="h-3 w-3 mr-1" />
                    JSON查看
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="relative">
                <ScrollArea class="h-[300px] w-full rounded-md border bg-muted/30">
                  <div class="p-4">
                    <pre class="text-sm whitespace-pre-wrap break-words font-mono">{{
                      taskData.result
                    }}</pre>
                  </div>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>

          <!-- 任务参数卡片 -->
          <Card v-if="taskParams" class="border-border/50">
            <CardHeader class="pb-3">
              <CardTitle class="text-base flex items-center gap-2">
                <MattIcon name="Settings" class="h-4 w-4" />
                任务参数
                <div class="ml-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    @click="copyToClipboard(JSON.stringify(taskParams, null, 2))"
                    class="h-8"
                  >
                    <MattIcon name="Copy" class="h-3 w-3 mr-1" />
                    复制参数
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea class="h-[200px] w-full rounded-md border bg-muted/30">
                <div class="p-4">
                  <pre class="text-sm whitespace-pre-wrap font-mono">{{
                    JSON.stringify(taskParams, null, 2)
                  }}</pre>
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          <!-- 时间线卡片 -->
          <Card class="border-border/50">
            <CardHeader class="pb-3">
              <CardTitle class="text-base flex items-center gap-2">
                <MattIcon name="Clock" class="h-4 w-4" />
                执行时间线
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="space-y-4">
                <div class="flex items-center gap-4 p-3 rounded-lg bg-muted/30">
                  <div class="w-2 h-2 rounded-full bg-blue-500"></div>
                  <div class="flex-1">
                    <div class="text-sm font-medium">任务创建</div>
                    <div class="text-xs text-muted-foreground">
                      {{ formatTimestamp(taskData.createTime) }}
                    </div>
                  </div>
                </div>

                <div
                  v-if="taskData.startTime"
                  class="flex items-center gap-4 p-3 rounded-lg bg-muted/30"
                >
                  <div class="w-2 h-2 rounded-full bg-green-500"></div>
                  <div class="flex-1">
                    <div class="text-sm font-medium">开始执行</div>
                    <div class="text-xs text-muted-foreground">
                      {{ formatTimestamp(taskData.startTime) }}
                    </div>
                  </div>
                </div>

                <div
                  v-if="taskData.endTime"
                  class="flex items-center gap-4 p-3 rounded-lg bg-muted/30"
                >
                  <div
                    class="w-2 h-2 rounded-full"
                    :class="getTimelineStatusColor(taskData.taskStatus)"
                  ></div>
                  <div class="flex-1">
                    <div class="text-sm font-medium">执行完成</div>
                    <div class="text-xs text-muted-foreground">
                      {{ formatTimestamp(taskData.endTime) }}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- 空状态 -->
        <div v-else class="flex flex-col items-center justify-center py-12 text-center">
          <MattIcon name="FileX" class="h-12 w-12 text-muted-foreground mb-4" />
          <h3 class="text-lg font-medium text-muted-foreground mb-2">暂无数据</h3>
          <p class="text-sm text-muted-foreground">请选择一个任务查看详细信息</p>
        </div>
      </div>

      <DialogFooter class="flex items-center justify-between">
        <div class="flex items-center gap-2 text-sm text-muted-foreground">
          <MattIcon name="Clock" class="h-4 w-4" />
          最后更新: {{ formatTimestamp(Date.now() / 1000) }}
        </div>
        <div class="flex items-center gap-2">
          <Button variant="outline" @click="refreshData" :disabled="loading">
            <MattIcon name="RefreshCw" class="h-4 w-4 mr-1" :class="{ 'animate-spin': loading }" />
            刷新
          </Button>
          <Button variant="outline" @click="closeDialog"> 关闭 </Button>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <!-- JSON 查看器 -->
  <MattJsonViewer v-model:is-open="jsonViewerOpen" :initial-data="jsonViewerData" />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { toast } from 'vue-sonner'
import { useTaskStore } from '@/store'
import { logger, formatDate, type Task } from '@mattverse/shared'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  Button,
  Badge,
  Progress,
  Label,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  ScrollArea,
  MattIcon,
  MattJsonViewer,
} from '@mattverse/mattverse-ui'

// Props 定义
interface Props {
  task?: Task | null
  open?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  task: null,
  open: false,
})

// Emits 定义
const emit = defineEmits<{
  'update:open': [value: boolean]
  refresh: []
}>()

// 使用 store
const taskStore = useTaskStore()

// 响应式数据
const isOpen = ref(false)
const loading = ref(false)
const taskData = ref<Task | null>(null)
const jsonViewerOpen = ref(false)
const jsonViewerData = ref<any>(null)

// 计算属性
const taskParams = computed(() => {
  if (!taskData.value) return null

  // 尝试从任务结果中提取参数信息
  try {
    const result = JSON.parse(taskData.value.result || '{}')
    return result.params || result.parameters || null
  } catch {
    return null
  }
})

// 工具方法
const { getStatusText, getStatusClass } = taskStore

// 从任务ID中提取服务名称
const getServiceName = (taskId: string): string => {
  const parts = taskId.split('::')
  return parts.length >= 2 ? parts[1] : taskId
}

// 获取有效的进度值
const getValidProgress = (progress: number | undefined | null): number => {
  if (progress === null || progress === undefined || isNaN(progress)) {
    return 0
  }
  return Math.max(0, Math.min(100, progress))
}

// 格式化时间戳
const formatTimestamp = (timestamp: number | string) => {
  if (!timestamp) return '-'
  const time = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp
  const date = new Date(time * 1000)
  return formatDate(date, 'YYYY/MM/DD HH:mm:ss')
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success('复制成功')
  } catch (err) {
    logger.error('复制失败:', err)
    toast.error('复制失败')
  }
}

// 检查是否为有效的JSON
const isValidJson = (str: string): boolean => {
  try {
    JSON.parse(str)
    return true
  } catch {
    return false
  }
}

// 打开JSON查看器
const openJsonViewer = () => {
  if (!taskData.value?.result) return

  try {
    const jsonData = JSON.parse(taskData.value.result)
    jsonViewerData.value = jsonData
    jsonViewerOpen.value = true
  } catch (err) {
    logger.error('解析JSON失败:', err)
    toast.error('JSON格式无效')
  }
}

// 获取时间线状态颜色
const getTimelineStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    Finished: 'bg-green-500',
    Error: 'bg-red-500',
    Abort: 'bg-red-500',
    Paused: 'bg-orange-500',
    Computing: 'bg-blue-500',
    Pending: 'bg-yellow-500',
    Initializing: 'bg-blue-500',
    TaskStay: 'bg-purple-500',
  }
  return colorMap[status] || 'bg-gray-500'
}

// 刷新数据
const refreshData = async () => {
  if (!taskData.value?.taskId) return

  loading.value = true
  try {
    await taskStore.updateTaskList(taskData.value.taskId)
    // 更新当前任务数据
    const updatedTask = taskStore.tasks.find(t => t.taskId === taskData.value?.taskId)
    if (updatedTask) {
      taskData.value = updatedTask
    }
    toast.success('数据已刷新')
    emit('refresh')
  } catch (error) {
    logger.error('刷新数据失败:', error)
    toast.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 关闭弹框
const closeDialog = () => {
  isOpen.value = false
  emit('update:open', false)
}

// 监听 props 变化
watch(
  () => props.open,
  newValue => {
    isOpen.value = newValue
  }
)

watch(
  () => props.task,
  newTask => {
    taskData.value = newTask
  }
)

watch(isOpen, newValue => {
  if (!newValue) {
    emit('update:open', false)
  }
})

// 暴露方法给父组件
defineExpose({
  refreshData,
  closeDialog,
})
</script>

<style scoped>
.dialog-body {
  max-height: 75vh;
  overflow-y: auto;
}

/* 自定义滚动条样式 */
.dialog-body::-webkit-scrollbar {
  width: 6px;
}

.dialog-body::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.dialog-body::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.dialog-body::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* 确保代码块的字体样式 */
pre {
  font-family:
    ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  line-height: 1.5;
}

/* 卡片悬停效果 */
.border-border\/50:hover {
  border-color: hsl(var(--border));
  transition: border-color 0.2s ease-in-out;
}

/* 时间线样式优化 */
.timeline-item {
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 100%;
  width: 1px;
  height: 16px;
  background: hsl(var(--border));
}
</style>
