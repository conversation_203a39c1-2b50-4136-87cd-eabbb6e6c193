<template>
  <Dialog v-model:open="isOpen" class="overflow-hidden">
    <DialogContent class="max-w-6xl max-h-[90vh] p-0 bg-background border-border">
      <!-- 标题栏 -->
      <div class="flex items-center justify-between px-4 py-3 border-b border-border bg-muted/30">
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-2">
            <MattIcon name="FileText" class="h-4 w-4 text-primary" />
            <h2 class="text-lg font-medium text-foreground">日志详情</h2>
          </div>
          <Badge
            v-if="taskData"
            :class="getStatusClass(taskData.taskStatus)"
            class="text-xs font-medium"
          >
            {{ getStatusText(taskData.taskStatus) }}
          </Badge>
        </div>
      </div>

      <!-- 内容区域 -->
      <div v-if="taskData" class="flex flex-col h-[calc(90vh-6rem)] overflow-hidden">
        <Tabs default-value="details" class="w-full">
          <TabsList class="w-full justify-start px-4 pt-2 bg-background border-b border-border">
            <TabsTrigger value="details" class="text-sm">详细信息</TabsTrigger>
            <TabsTrigger value="logs" class="text-sm">日志信息</TabsTrigger>
            <TabsTrigger value="results" class="text-sm">计算结果</TabsTrigger>
          </TabsList>

          <!-- 详细信息标签页 -->
          <TabsContent value="details" class="p-0 m-0 h-full">
            <ScrollArea class="h-[calc(90vh-10rem)]">
              <div class="p-4 space-y-4">
                <!-- 基本信息卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card class="border border-border shadow-sm bg-card/50">
                    <CardHeader class="p-3 pb-0">
                      <CardTitle class="text-sm font-medium flex items-center gap-2">
                        <MattIcon name="Server" class="h-3.5 w-3.5 text-primary" />
                        服务信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent class="p-3 pt-2">
                      <div class="text-sm">
                        <div class="font-medium text-foreground">
                          {{ getServiceName(taskData.taskId) }}
                        </div>
                        <div class="text-xs text-muted-foreground mt-1">
                          服务ID: {{ taskData.taskId.split('::')[0] || '--' }}
                        </div>
                        <div class="text-xs text-muted-foreground mt-1">
                          任务ID: {{ taskData.taskId }}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card class="border border-border shadow-sm bg-card/50">
                    <CardHeader class="p-3 pb-0">
                      <CardTitle class="text-sm font-medium flex items-center gap-2">
                        <MattIcon name="Clock" class="h-3.5 w-3.5 text-primary" />
                        时间信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent class="p-3 pt-2">
                      <div class="text-sm">
                        <div class="font-medium text-foreground">
                          {{ taskData.duration || '未开始' }}
                        </div>
                        <div class="text-xs text-muted-foreground mt-1">
                          开始: {{ formatTimestamp(taskData.startTime) }}
                          {{
                            taskData.endTime && taskData.endTime !== 0
                              ? ' / 结束: ' + formatTimestamp(taskData.endTime)
                              : ''
                          }}
                        </div>
                        <div class="text-xs text-muted-foreground mt-1">
                          创建时间: {{ formatTimestamp(taskData.createTime) }}
                        </div>
                        <div
                          v-if="taskData.taskProcess !== undefined"
                          class="text-xs text-muted-foreground mt-1"
                        >
                          进度: {{ taskData.taskProcess.toFixed(1) }}%
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>

          <!-- 日志信息标签页 -->
          <TabsContent value="logs" class="p-0 m-0 h-full">
            <div class="flex flex-col h-full">
              <div
                class="flex items-center justify-between px-4 py-2 bg-muted/20 border-b border-border"
              >
                <div class="text-sm font-medium">日志信息</div>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="copyToClipboard(taskData.taskLog || '暂无日志信息')"
                >
                  <MattIcon name="Copy" class="h-3.5 w-3.5 mr-1.5" />
                  <span class="text-xs">复制</span>
                </Button>
              </div>
              <ScrollArea class="h-[calc(90vh-14rem)]">
                <div class="bg-muted/10 h-full">
                  <pre class="h-[calc(90vh-14rem)] p-4 rounded-md">
                    <code class="text-xs break-words" v-html="highlightJson(taskData.taskLog)"></code>
                  </pre>
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          <!-- 计算结果标签页 -->
          <TabsContent value="results" class="p-0 m-0 h-full">
            <div class="flex flex-col h-full">
              <div
                class="flex items-center justify-between px-4 py-2 bg-muted/20 border-b border-border"
              >
                <div class="text-sm font-medium">计算结果</div>
                <div class="flex items-center gap-2">
                  <Button variant="outline" size="sm" class="h-8" @click="openJsonViewer">
                    <MattIcon name="Eye" class="h-3.5 w-3.5 mr-1.5" />
                    <span class="text-xs">查看原始数据</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    class="h-8"
                    @click="copyToClipboard(taskResult)"
                  >
                    <MattIcon name="Copy" class="h-3.5 w-3.5 mr-1.5" />
                    <span class="text-xs">复制</span>
                  </Button>
                </div>
              </div>
              <ScrollArea class="h-[calc(90vh-14rem)]">
                <div class="p-4 space-y-4">
                  <!-- 无数据状态 -->
                  <div
                    v-if="!parsedResult || taskResult === '--'"
                    class="flex flex-col items-center justify-center py-12 text-center"
                  >
                    <MattIcon name="FileX" class="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 class="text-lg font-medium text-muted-foreground mb-2">暂无计算结果数据</h3>
                    <p class="text-sm text-muted-foreground">当前没有可显示的计算结果</p>
                  </div>

                  <!-- 通用结果展示 -->
                  <div v-else-if="typeof parsedResult === 'object'" class="space-y-4">
                    <!-- 遍历顶层属性，为每个属性创建卡片 -->
                    <div v-for="(value, key) in parsedResult" :key="key" class="space-y-2">
                      <h3 class="text-sm font-medium text-foreground">{{ key }}</h3>
                      <!-- 递归组件：处理嵌套对象和数组 -->
                      <MattJsonDataRenderer :data="value" :property-name="key" />
                    </div>
                  </div>

                  <!-- 非对象类型结果展示 -->
                  <div v-else class="p-3 border border-border rounded-md bg-card/30">
                    <div class="text-sm whitespace-pre-wrap break-words">{{ taskResult }}</div>
                  </div>
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <!-- 加载状态 -->
      <div v-else class="flex justify-center items-center h-[calc(90vh-6rem)]">
        <div class="flex flex-col items-center gap-2 text-muted-foreground">
          <MattIcon name="Loader2" class="h-6 w-6 animate-spin" />
          <span class="text-sm">加载中...</span>
        </div>
      </div>
    </DialogContent>
  </Dialog>

  <!-- JSON 查看器 -->
  <MattJsonViewer v-model:is-open="jsonViewerOpen" :initial-data="jsonViewerData" />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { toast } from 'vue-sonner'
import { useTaskStore } from '@/store'
import { logger, formatDate, type Task } from '@mattverse/shared'
import {
  Dialog,
  DialogContent,
  Button,
  Badge,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  ScrollArea,
  MattIcon,
  MattJsonViewer,
  MattJsonDataRenderer,
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from '@mattverse/mattverse-ui'

// Props 定义
interface Props {
  task?: Task | null
  open?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  task: null,
  open: false,
})

// Emits 定义
const emit = defineEmits<{
  'update:open': [value: boolean]
  refresh: []
}>()

// 使用 store
const taskStore = useTaskStore()

// 响应式数据
const isOpen = ref(false)
const taskData = ref<Task | null>(null)
const jsonViewerOpen = ref(false)
const jsonViewerData = ref<any>(null)

// 安全的 JSON 序列化函数，避免循环引用
const safeStringify = (obj: any): string => {
  try {
    // 创建一个 Set 来跟踪已访问的对象，避免循环引用
    const seen = new WeakSet()
    return JSON.stringify(
      obj,
      (_key, val) => {
        if (val != null && typeof val === 'object') {
          if (seen.has(val)) {
            return '[Circular Reference]'
          }
          seen.add(val)
        }
        return val
      },
      2
    )
  } catch (error) {
    logger.error('JSON序列化失败:', error)
    return String(obj)
  }
}

// 计算属性
const taskResult = computed(() => {
  if (!taskData.value) return '--'

  // 优先显示 taskData.result
  if (taskData.value.result && taskData.value.result.trim() !== '') return taskData.value.result

  // 其次显示 store 缓存的结果
  const storeResult = taskStore.getTaskResultById?.(taskData.value.taskId)?.value

  if (storeResult && typeof storeResult === 'string' && storeResult.trim() !== '')
    return storeResult
  if (storeResult && typeof storeResult === 'object') {
    // 使用安全的序列化函数
    return safeStringify(storeResult)
  }

  return '--'
})

// 解析结果数据为结构化对象
const parsedResult = computed(() => {
  if (!taskResult.value || taskResult.value === '--') return null

  try {
    // 如果是字符串，尝试解析为JSON对象
    if (typeof taskResult.value === 'string') {
      return JSON.parse(taskResult.value)
    }
    // 如果已经是对象，直接返回
    return taskResult.value
  } catch (e) {
    logger.error('解析结果数据失败:', e)
    return null
  }
})

// 工具方法
const { getStatusText, getStatusClass } = taskStore

// 从任务ID中提取服务名称
const getServiceName = (taskId: string): string => {
  const parts = taskId.split('::')
  return parts.length >= 2 ? parts[1] : taskId
}

// 格式化时间戳
const formatTimestamp = (timestamp: number | string) => {
  if (!timestamp) return '-'
  const time = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp
  const date = new Date(time * 1000)
  return formatDate(date, 'YYYY/MM/DD HH:mm:ss')
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success('复制成功')
  } catch (err) {
    logger.error('复制失败:', err)
    toast.error('复制失败')
  }
}

// 高亮显示JSON
const highlightJson = (jsonString?: string) => {
  if (!jsonString || jsonString === '--') return jsonString || '--'
  try {
    // 只高亮合法 JSON，否则直接返回原字符串
    const obj = typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString
    const formatted = JSON.stringify(obj, null, 2)
    // 简单的语法高亮，不依赖 Prism
    return formatted
      .replace(/(".*?")/g, '<span style="color: #22c55e;">$1</span>') // 字符串 - 绿色
      .replace(/(\d+)/g, '<span style="color: #f97316;">$1</span>') // 数字 - 橙色
      .replace(/(true|false)/g, '<span style="color: #8b5cf6;">$1</span>') // 布尔值 - 紫色
      .replace(/(null)/g, '<span style="color: #6b7280; font-style: italic;">$1</span>') // null - 灰色斜体
  } catch {
    return jsonString.replace(/(.{120})/g, '$1\n') // 每120字符换行
  }
}

// 打开JSON查看器
const openJsonViewer = () => {
  if (taskResult.value && taskResult.value !== '--') {
    try {
      // 如果是字符串，尝试解析为JSON对象
      if (typeof taskResult.value === 'string') {
        jsonViewerData.value = JSON.parse(taskResult.value)
      } else {
        // 如果已经是对象，直接使用
        jsonViewerData.value = taskResult.value
      }
      jsonViewerOpen.value = true
    } catch (err) {
      logger.error('解析JSON失败:', err)
      toast.error('JSON格式无效')
    }
  } else {
    toast.warning('暂无计算结果可查看')
  }
}

// 关闭弹框
const closeDialog = () => {
  isOpen.value = false
  emit('update:open', false)
}

// 监听 props 变化
watch(
  () => props.open,
  newValue => {
    isOpen.value = newValue
    if (newValue && props.task?.taskId) {
      // 当弹框打开时，尝试获取最新的任务结果
      taskStore.updateTaskResult(props.task.taskId)
    }
  },
  { immediate: true }
)

watch(
  () => props.task,
  newTask => {
    taskData.value = newTask
  },
  { immediate: true }
)

watch(isOpen, newValue => {
  if (!newValue) {
    emit('update:open', false)
  }
})

// 暴露方法给父组件
defineExpose({
  closeDialog,
})
</script>

<style scoped>
.dialog-body {
  max-height: 75vh;
  overflow-y: auto;
}

/* 自定义滚动条样式 */
.dialog-body::-webkit-scrollbar {
  width: 6px;
}

.dialog-body::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.dialog-body::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.dialog-body::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* 确保代码块的字体样式 */
pre {
  font-family:
    ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  line-height: 1.5;
}

/* 卡片悬停效果 */
.border-border\/50:hover {
  border-color: hsl(var(--border));
  transition: border-color 0.2s ease-in-out;
}

/* 时间线样式优化 */
.timeline-item {
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 100%;
  width: 1px;
  height: 16px;
  background: hsl(var(--border));
}
</style>
