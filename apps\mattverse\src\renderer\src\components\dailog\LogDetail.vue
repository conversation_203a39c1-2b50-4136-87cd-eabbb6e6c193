<template>
  <Dialog v-model:open="isOpen" class="overflow-hidden">
    <DialogContent class="max-w-6xl max-h-[90vh] p-0 bg-background border-border">
      <!-- 标题栏 -->
      <div class="flex items-center justify-between px-4 py-3 border-b border-border bg-muted/30">
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-2">
            <MattIcon name="FileText" class="h-4 w-4 text-primary" />
            <h2 class="text-lg font-medium text-foreground">日志详情</h2>
          </div>
          <Badge
            v-if="taskData"
            :class="getStatusClass(taskData.taskStatus)"
            class="text-xs font-medium"
          >
            {{ getStatusText(taskData.taskStatus) }}
          </Badge>
        </div>
      </div>

      <!-- 内容区域 -->
      <div v-if="taskData" class="p-4">
        <Tabs default-value="details" class="w-full">
          <TabsList class="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="details" class="text-sm">详细信息</TabsTrigger>
            <TabsTrigger value="logs" class="text-sm">日志信息</TabsTrigger>
            <TabsTrigger value="results" class="text-sm">计算结果</TabsTrigger>
          </TabsList>

          <!-- 详细信息标签页 -->
          <TabsContent value="details" class="space-y-6">
            <!-- 基本信息卡片 -->
            <Card class="border-border/50">
              <CardHeader class="pb-3">
                <CardTitle class="text-base flex items-center gap-2">
                  <MattIcon name="Info" class="h-4 w-4" />
                  基本信息
                </CardTitle>
              </CardHeader>
              <CardContent class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <!-- 任务ID -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-muted-foreground">任务ID</Label>
                    <div class="flex items-center gap-2">
                      <div class="p-2 bg-muted rounded font-mono text-sm flex-1 break-all">
                        {{ taskData.taskId }}
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        class="h-8 w-8 flex-shrink-0"
                        @click="copyToClipboard(taskData.taskId)"
                        title="复制任务ID"
                      >
                        <MattIcon name="Copy" class="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  <!-- 服务名称 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-muted-foreground">服务名称</Label>
                    <div class="p-2 bg-muted rounded text-sm">
                      {{ getServiceName(taskData.taskId) }}
                    </div>
                  </div>

                  <!-- 运行状态 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-muted-foreground">运行状态</Label>
                    <div class="flex items-center">
                      <Badge :class="getStatusClass(taskData.taskStatus)">
                        {{ getStatusText(taskData.taskStatus) }}
                      </Badge>
                    </div>
                  </div>

                  <!-- 进度 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-muted-foreground">执行进度</Label>
                    <div class="flex items-center gap-2">
                      <Progress
                        :model-value="getValidProgress(taskData.taskProcess)"
                        class="flex-1"
                      />
                      <span class="text-sm font-medium min-w-[50px]">
                        {{ (taskData.taskProcess || 0).toFixed(1) }}%
                      </span>
                    </div>
                  </div>

                  <!-- 提交时间 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-muted-foreground">提交时间</Label>
                    <div class="p-2 bg-muted rounded text-sm">
                      {{ formatTimestamp(taskData.createTime) }}
                    </div>
                  </div>

                  <!-- 运行时间 -->
                  <div class="space-y-2">
                    <Label class="text-sm font-medium text-muted-foreground">运行时间</Label>
                    <div class="p-2 bg-muted rounded text-sm">
                      {{ taskData.duration || '未开始' }}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- 时间线卡片 -->
            <Card class="border-border/50">
              <CardHeader class="pb-3">
                <CardTitle class="text-base flex items-center gap-2">
                  <MattIcon name="Clock" class="h-4 w-4" />
                  执行时间线
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div class="flex items-center gap-4 p-3 rounded-lg bg-muted/30">
                    <div class="w-2 h-2 rounded-full bg-blue-500"></div>
                    <div class="flex-1">
                      <div class="text-sm font-medium">任务创建</div>
                      <div class="text-xs text-muted-foreground">
                        {{ formatTimestamp(taskData.createTime) }}
                      </div>
                    </div>
                  </div>

                  <div
                    v-if="taskData.startTime"
                    class="flex items-center gap-4 p-3 rounded-lg bg-muted/30"
                  >
                    <div class="w-2 h-2 rounded-full bg-green-500"></div>
                    <div class="flex-1">
                      <div class="text-sm font-medium">开始执行</div>
                      <div class="text-xs text-muted-foreground">
                        {{ formatTimestamp(taskData.startTime) }}
                      </div>
                    </div>
                  </div>

                  <div
                    v-if="taskData.endTime"
                    class="flex items-center gap-4 p-3 rounded-lg bg-muted/30"
                  >
                    <div
                      class="w-2 h-2 rounded-full"
                      :class="getTimelineStatusColor(taskData.taskStatus)"
                    ></div>
                    <div class="flex-1">
                      <div class="text-sm font-medium">执行完成</div>
                      <div class="text-xs text-muted-foreground">
                        {{ formatTimestamp(taskData.endTime) }}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 日志信息标签页 -->
          <TabsContent value="logs" class="space-y-4">
            <Card class="border-border/50">
              <CardHeader class="pb-3">
                <CardTitle class="text-base flex items-center gap-2">
                  <MattIcon name="Terminal" class="h-4 w-4" />
                  日志信息
                  <div class="ml-auto">
                    <Button
                      variant="outline"
                      size="sm"
                      @click="copyToClipboard(taskData.taskLog || '暂无日志信息')"
                      class="h-8"
                    >
                      <MattIcon name="Copy" class="h-3 w-3 mr-1" />
                      复制
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="relative">
                  <ScrollArea class="h-[400px] w-full rounded-md border">
                    <div class="terminal-bg h-full">
                      <pre class="p-4 text-sm font-mono leading-relaxed min-h-[400px] w-full">
                        <code v-html="highlightJson(taskData.taskLog || '暂无日志信息')"></code>
                      </pre>
                    </div>
                  </ScrollArea>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <!-- 计算结果标签页 -->
          <TabsContent value="results" class="space-y-4">
            <Card class="border-border/50">
              <CardHeader class="pb-3">
                <CardTitle class="text-base flex items-center gap-2">
                  <MattIcon name="BarChart3" class="h-4 w-4" />
                  计算结果
                  <div class="ml-auto flex items-center gap-2">
                    <Button variant="outline" size="sm" class="h-8" @click="openJsonViewer">
                      <MattIcon name="Eye" class="h-3 w-3 mr-1" />
                      查看原始数据
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      class="h-8"
                      @click="copyToClipboard(taskResult)"
                    >
                      <MattIcon name="Copy" class="h-3 w-3 mr-1" />
                      复制
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea class="h-[400px] w-full rounded-md border bg-muted/30">
                  <div class="p-4 space-y-4">
                    <!-- 无数据状态 -->
                    <div
                      v-if="!parsedResult || taskResult === '--'"
                      class="flex flex-col items-center justify-center py-12 text-center"
                    >
                      <MattIcon name="FileX" class="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 class="text-lg font-medium text-muted-foreground mb-2">
                        暂无计算结果数据
                      </h3>
                      <p class="text-sm text-muted-foreground">当前没有可显示的计算结果</p>
                    </div>

                    <!-- 通用结果展示 -->
                    <div v-else-if="typeof parsedResult === 'object'" class="space-y-4">
                      <!-- 遍历顶层属性，为每个属性创建卡片 -->
                      <div v-for="(value, key) in parsedResult" :key="key" class="space-y-2">
                        <h3 class="text-sm font-medium text-foreground">{{ key }}</h3>
                        <!-- 递归组件：处理嵌套对象和数组 -->
                        <MattJsonDataRenderer :data="value" :property-name="String(key)" />
                      </div>
                    </div>

                    <!-- 非对象类型结果展示 -->
                    <div v-else class="p-3 border border-border rounded-md bg-card/30">
                      <div class="text-sm whitespace-pre-wrap break-words">{{ taskResult }}</div>
                    </div>
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <!-- 加载状态 -->
      <div v-else class="flex justify-center items-center h-[calc(90vh-6rem)]">
        <div class="flex flex-col items-center gap-2 text-muted-foreground">
          <MattIcon name="Loader2" class="h-6 w-6 animate-spin" />
          <span class="text-sm">加载中...</span>
        </div>
      </div>
    </DialogContent>
  </Dialog>

  <!-- JSON 查看器 -->
  <MattJsonViewer v-model:is-open="jsonViewerOpen" :initial-data="jsonViewerData" />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { toast } from 'vue-sonner'
import { useTaskStore } from '@/store'
import { logger, formatDate, type Task } from '@mattverse/shared'
import Prism from 'prismjs'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import {
  Dialog,
  DialogContent,
  Button,
  Badge,
  Progress,
  Label,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  ScrollArea,
  MattIcon,
  MattJsonViewer,
  MattJsonDataRenderer,
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from '@mattverse/mattverse-ui'

// Props 定义
interface Props {
  task?: Task | null
  open?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  task: null,
  open: false,
})

// Emits 定义
const emit = defineEmits<{
  'update:open': [value: boolean]
  refresh: []
}>()

// 使用 store
const taskStore = useTaskStore()

// 响应式数据
const isOpen = ref(false)
const taskData = ref<Task | null>(null)
const jsonViewerOpen = ref(false)
const jsonViewerData = ref<any>(null)

// 安全的 JSON 序列化函数，避免循环引用
const safeStringify = (obj: any): string => {
  try {
    // 创建一个 Set 来跟踪已访问的对象，避免循环引用
    const seen = new WeakSet()
    return JSON.stringify(
      obj,
      (_key, val) => {
        if (val != null && typeof val === 'object') {
          if (seen.has(val)) {
            return '[Circular Reference]'
          }
          seen.add(val)
        }
        return val
      },
      2
    )
  } catch (error) {
    logger.error('JSON序列化失败:', error)
    return String(obj)
  }
}

// 计算属性
const taskResult = computed(() => {
  if (!taskData.value) return '--'

  // 优先显示 taskData.result
  if (taskData.value.result && taskData.value.result.trim() !== '') return taskData.value.result

  // 其次显示 store 缓存的结果
  const storeResult = taskStore.getTaskResultById?.(taskData.value.taskId)?.value

  if (storeResult && typeof storeResult === 'string' && storeResult.trim() !== '')
    return storeResult
  if (storeResult && typeof storeResult === 'object') {
    // 使用安全的序列化函数
    return safeStringify(storeResult)
  }

  return '--'
})

// 解析结果数据为结构化对象
const parsedResult = computed(() => {
  if (!taskResult.value || taskResult.value === '--') return null

  try {
    // 如果是字符串，尝试解析为JSON对象
    if (typeof taskResult.value === 'string') {
      return JSON.parse(taskResult.value)
    }
    // 如果已经是对象，直接返回
    return taskResult.value
  } catch (e) {
    logger.error('解析结果数据失败:', e)
    return null
  }
})

// 工具方法
const { getStatusText, getStatusClass } = taskStore

// 从任务ID中提取服务名称
const getServiceName = (taskId: string): string => {
  const parts = taskId.split('::')
  return parts.length >= 2 ? parts[1] : taskId
}

// 获取有效的进度值
const getValidProgress = (progress: number | undefined | null): number => {
  if (progress === null || progress === undefined || isNaN(progress)) {
    return 0
  }
  return Math.max(0, Math.min(100, progress))
}

// 获取时间线状态颜色
const getTimelineStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    Finished: 'bg-green-500',
    Error: 'bg-red-500',
    Abort: 'bg-red-500',
    Paused: 'bg-orange-500',
    Computing: 'bg-blue-500',
    Pending: 'bg-yellow-500',
    Initializing: 'bg-blue-500',
    TaskStay: 'bg-purple-500',
  }
  return colorMap[status] || 'bg-gray-500'
}

// 格式化时间戳
const formatTimestamp = (timestamp: number | string) => {
  if (!timestamp) return '-'
  const time = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp
  const date = new Date(time * 1000)
  return formatDate(date, 'YYYY/MM/DD HH:mm:ss')
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success('复制成功')
  } catch (err) {
    logger.error('复制失败:', err)
    toast.error('复制失败')
  }
}

// 高亮显示JSON
const highlightJson = (jsonString?: string) => {
  if (!jsonString || jsonString === '--') return jsonString || '--'
  try {
    // 只高亮合法 JSON，否则直接返回原字符串
    const obj = typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString
    const formatted = JSON.stringify(obj, null, 2)
    // 使用 Prism 高亮显示
    return Prism.highlight(formatted, Prism.languages.json, 'json')
  } catch {
    // 如果解析失败，仍然尝试高亮显示原始文本
    return Prism.highlight(jsonString, Prism.languages.json, 'json')
  }
}

// 打开JSON查看器
const openJsonViewer = () => {
  if (taskResult.value && taskResult.value !== '--') {
    try {
      // 如果是字符串，尝试解析为JSON对象
      if (typeof taskResult.value === 'string') {
        jsonViewerData.value = JSON.parse(taskResult.value)
      } else {
        // 如果已经是对象，直接使用
        jsonViewerData.value = taskResult.value
      }
      jsonViewerOpen.value = true
    } catch (err) {
      logger.error('解析JSON失败:', err)
      toast.error('JSON格式无效')
    }
  } else {
    toast.warning('暂无计算结果可查看')
  }
}

// 关闭弹框
const closeDialog = () => {
  isOpen.value = false
  emit('update:open', false)
}

// 监听 props 变化
watch(
  () => props.open,
  newValue => {
    isOpen.value = newValue
    if (newValue && props.task?.taskId) {
      // 当弹框打开时，尝试获取最新的任务结果
      taskStore.updateTaskResult(props.task.taskId)
    }
  },
  { immediate: true }
)

watch(
  () => props.task,
  newTask => {
    taskData.value = newTask
  },
  { immediate: true }
)

watch(isOpen, newValue => {
  if (!newValue) {
    emit('update:open', false)
  }
})

// 暴露方法给父组件
defineExpose({
  closeDialog,
})
</script>

<style scoped>
/* 终端背景样式 */
.terminal-bg {
  background: #1e1e1e;
  color: #d4d4d4;
  min-height: 100%;
}

.terminal-bg pre {
  background: transparent;
  color: inherit;
  margin: 0;
  height: 100%;
  box-sizing: border-box;
}

.terminal-bg code {
  background: transparent;
  color: inherit;
  font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  display: block;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 确保代码块的字体样式 */
pre {
  font-family:
    ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  line-height: 1.5;
}

/* 卡片悬停效果 */
.border-border\/50:hover {
  border-color: hsl(var(--border));
  transition: border-color 0.2s ease-in-out;
}

/* 时间线样式优化 */
.timeline-item {
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 100%;
  width: 1px;
  height: 16px;
  background: hsl(var(--border));
}

/* Prism 主题覆盖 - 终端风格 */
:deep(.token.property) {
  color: #9cdcfe;
}

:deep(.token.string) {
  color: #ce9178;
}

:deep(.token.number) {
  color: #b5cea8;
}

:deep(.token.boolean) {
  color: #569cd6;
}

:deep(.token.null) {
  color: #569cd6;
}

:deep(.token.punctuation) {
  color: #d4d4d4;
}

:deep(.token.keyword) {
  color: #c586c0;
}
</style>
