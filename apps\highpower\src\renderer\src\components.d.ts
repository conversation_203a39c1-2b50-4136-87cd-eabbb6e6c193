/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Accordion: typeof import('@mattverse/mattverse-ui')['Accordion']
    AccordionContent: typeof import('@mattverse/mattverse-ui')['AccordionContent']
    AccordionItem: typeof import('@mattverse/mattverse-ui')['AccordionItem']
    AccordionTrigger: typeof import('@mattverse/mattverse-ui')['AccordionTrigger']
    Alert: typeof import('@mattverse/mattverse-ui')['Alert']
    AlertDescription: typeof import('@mattverse/mattverse-ui')['AlertDescription']
    AlertDialog: typeof import('@mattverse/mattverse-ui')['AlertDialog']
    AlertDialogAction: typeof import('@mattverse/mattverse-ui')['AlertDialogAction']
    AlertDialogCancel: typeof import('@mattverse/mattverse-ui')['AlertDialogCancel']
    AlertDialogContent: typeof import('@mattverse/mattverse-ui')['AlertDialogContent']
    AlertDialogDescription: typeof import('@mattverse/mattverse-ui')['AlertDialogDescription']
    AlertDialogFooter: typeof import('@mattverse/mattverse-ui')['AlertDialogFooter']
    AlertDialogHeader: typeof import('@mattverse/mattverse-ui')['AlertDialogHeader']
    AlertDialogTitle: typeof import('@mattverse/mattverse-ui')['AlertDialogTitle']
    AlertDialogTrigger: typeof import('@mattverse/mattverse-ui')['AlertDialogTrigger']
    AlertTitle: typeof import('@mattverse/mattverse-ui')['AlertTitle']
    Avatar: typeof import('@mattverse/mattverse-ui')['Avatar']
    AvatarFallback: typeof import('@mattverse/mattverse-ui')['AvatarFallback']
    AvatarImage: typeof import('@mattverse/mattverse-ui')['AvatarImage']
    Badge: typeof import('@mattverse/mattverse-ui')['Badge']
    Breadcrumb: typeof import('@mattverse/mattverse-ui')['Breadcrumb']
    BreadcrumbItem: typeof import('@mattverse/mattverse-ui')['BreadcrumbItem']
    BreadcrumbLink: typeof import('@mattverse/mattverse-ui')['BreadcrumbLink']
    BreadcrumbList: typeof import('@mattverse/mattverse-ui')['BreadcrumbList']
    BreadcrumbPage: typeof import('@mattverse/mattverse-ui')['BreadcrumbPage']
    BreadcrumbSeparator: typeof import('@mattverse/mattverse-ui')['BreadcrumbSeparator']
    Button: typeof import('@mattverse/mattverse-ui')['Button']
    CalendarIcon: typeof import('lucide-vue-next')['CalendarIcon']
    Card: typeof import('@mattverse/mattverse-ui')['Card']
    CardContent: typeof import('@mattverse/mattverse-ui')['CardContent']
    CardDescription: typeof import('@mattverse/mattverse-ui')['CardDescription']
    CardFooter: typeof import('@mattverse/mattverse-ui')['CardFooter']
    CardHeader: typeof import('@mattverse/mattverse-ui')['CardHeader']
    CardTitle: typeof import('@mattverse/mattverse-ui')['CardTitle']
    Carousel: typeof import('@mattverse/mattverse-ui')['Carousel']
    CarouselContent: typeof import('@mattverse/mattverse-ui')['CarouselContent']
    CarouselItem: typeof import('@mattverse/mattverse-ui')['CarouselItem']
    CarouselNext: typeof import('@mattverse/mattverse-ui')['CarouselNext']
    CarouselPrevious: typeof import('@mattverse/mattverse-ui')['CarouselPrevious']
    Checkbox: typeof import('@mattverse/mattverse-ui')['Checkbox']
    ChevronsUpDown: typeof import('lucide-vue-next')['ChevronsUpDown']
    Collapsible: typeof import('@mattverse/mattverse-ui')['Collapsible']
    CollapsibleContent: typeof import('@mattverse/mattverse-ui')['CollapsibleContent']
    CollapsibleTrigger: typeof import('@mattverse/mattverse-ui')['CollapsibleTrigger']
    Combobox: typeof import('@mattverse/mattverse-ui')['Combobox']
    ComboboxAnchor: typeof import('@mattverse/mattverse-ui')['ComboboxAnchor']
    ComboboxEmpty: typeof import('@mattverse/mattverse-ui')['ComboboxEmpty']
    ComboboxGroup: typeof import('@mattverse/mattverse-ui')['ComboboxGroup']
    ComboboxInput: typeof import('@mattverse/mattverse-ui')['ComboboxInput']
    ComboboxItem: typeof import('@mattverse/mattverse-ui')['ComboboxItem']
    ComboboxItemIndicator: typeof import('@mattverse/mattverse-ui')['ComboboxItemIndicator']
    ComboboxList: typeof import('@mattverse/mattverse-ui')['ComboboxList']
    Command: typeof import('@mattverse/mattverse-ui')['Command']
    CommandEmpty: typeof import('@mattverse/mattverse-ui')['CommandEmpty']
    CommandGroup: typeof import('@mattverse/mattverse-ui')['CommandGroup']
    CommandInput: typeof import('@mattverse/mattverse-ui')['CommandInput']
    CommandItem: typeof import('@mattverse/mattverse-ui')['CommandItem']
    CommandList: typeof import('@mattverse/mattverse-ui')['CommandList']
    CommandSeparator: typeof import('@mattverse/mattverse-ui')['CommandSeparator']
    CommandShortcut: typeof import('@mattverse/mattverse-ui')['CommandShortcut']
    ContextMenu: typeof import('@mattverse/mattverse-ui')['ContextMenu']
    ContextMenuCheckboxItem: typeof import('@mattverse/mattverse-ui')['ContextMenuCheckboxItem']
    ContextMenuContent: typeof import('@mattverse/mattverse-ui')['ContextMenuContent']
    ContextMenuItem: typeof import('@mattverse/mattverse-ui')['ContextMenuItem']
    ContextMenuLabel: typeof import('@mattverse/mattverse-ui')['ContextMenuLabel']
    ContextMenuRadioGroup: typeof import('@mattverse/mattverse-ui')['ContextMenuRadioGroup']
    ContextMenuRadioItem: typeof import('@mattverse/mattverse-ui')['ContextMenuRadioItem']
    ContextMenuSeparator: typeof import('@mattverse/mattverse-ui')['ContextMenuSeparator']
    ContextMenuShortcut: typeof import('@mattverse/mattverse-ui')['ContextMenuShortcut']
    ContextMenuSub: typeof import('@mattverse/mattverse-ui')['ContextMenuSub']
    ContextMenuSubContent: typeof import('@mattverse/mattverse-ui')['ContextMenuSubContent']
    ContextMenuSubTrigger: typeof import('@mattverse/mattverse-ui')['ContextMenuSubTrigger']
    ContextMenuTrigger: typeof import('@mattverse/mattverse-ui')['ContextMenuTrigger']
    Copy: typeof import('lucide-vue-next')['Copy']
    Dialog: typeof import('@mattverse/mattverse-ui')['Dialog']
    DialogClose: typeof import('@mattverse/mattverse-ui')['DialogClose']
    DialogContent: typeof import('@mattverse/mattverse-ui')['DialogContent']
    DialogDescription: typeof import('@mattverse/mattverse-ui')['DialogDescription']
    DialogFooter: typeof import('@mattverse/mattverse-ui')['DialogFooter']
    DialogHeader: typeof import('@mattverse/mattverse-ui')['DialogHeader']
    DialogTitle: typeof import('@mattverse/mattverse-ui')['DialogTitle']
    DialogTrigger: typeof import('@mattverse/mattverse-ui')['DialogTrigger']
    Drawer: typeof import('@mattverse/mattverse-ui')['Drawer']
    DrawerClose: typeof import('@mattverse/mattverse-ui')['DrawerClose']
    DrawerContent: typeof import('@mattverse/mattverse-ui')['DrawerContent']
    DrawerDescription: typeof import('@mattverse/mattverse-ui')['DrawerDescription']
    DrawerHeader: typeof import('@mattverse/mattverse-ui')['DrawerHeader']
    DrawerTitle: typeof import('@mattverse/mattverse-ui')['DrawerTitle']
    DrawerTrigger: typeof import('@mattverse/mattverse-ui')['DrawerTrigger']
    DropdownMenu: typeof import('@mattverse/mattverse-ui')['DropdownMenu']
    DropdownMenuContent: typeof import('@mattverse/mattverse-ui')['DropdownMenuContent']
    DropdownMenuLabel: typeof import('@mattverse/mattverse-ui')['DropdownMenuLabel']
    DropdownMenuRadioGroup: typeof import('@mattverse/mattverse-ui')['DropdownMenuRadioGroup']
    DropdownMenuRadioItem: typeof import('@mattverse/mattverse-ui')['DropdownMenuRadioItem']
    DropdownMenuSeparator: typeof import('@mattverse/mattverse-ui')['DropdownMenuSeparator']
    DropdownMenuTrigger: typeof import('@mattverse/mattverse-ui')['DropdownMenuTrigger']
    HoverCard: typeof import('@mattverse/mattverse-ui')['HoverCard']
    HoverCardContent: typeof import('@mattverse/mattverse-ui')['HoverCardContent']
    HoverCardTrigger: typeof import('@mattverse/mattverse-ui')['HoverCardTrigger']
    Input: typeof import('@mattverse/mattverse-ui')['Input']
    Label: typeof import('@mattverse/mattverse-ui')['Label']
    MattSvg: typeof import('@mattverse/mattverse-ui')['MattSvg']
    Menubar: typeof import('@mattverse/mattverse-ui')['Menubar']
    MenubarCheckboxItem: typeof import('@mattverse/mattverse-ui')['MenubarCheckboxItem']
    MenubarContent: typeof import('@mattverse/mattverse-ui')['MenubarContent']
    MenubarItem: typeof import('@mattverse/mattverse-ui')['MenubarItem']
    MenubarMenu: typeof import('@mattverse/mattverse-ui')['MenubarMenu']
    MenubarRadioGroup: typeof import('@mattverse/mattverse-ui')['MenubarRadioGroup']
    MenubarRadioItem: typeof import('@mattverse/mattverse-ui')['MenubarRadioItem']
    MenubarSeparator: typeof import('@mattverse/mattverse-ui')['MenubarSeparator']
    MenubarShortcut: typeof import('@mattverse/mattverse-ui')['MenubarShortcut']
    MenubarSub: typeof import('@mattverse/mattverse-ui')['MenubarSub']
    MenubarSubContent: typeof import('@mattverse/mattverse-ui')['MenubarSubContent']
    MenubarSubTrigger: typeof import('@mattverse/mattverse-ui')['MenubarSubTrigger']
    MenubarTrigger: typeof import('@mattverse/mattverse-ui')['MenubarTrigger']
    NavigationMenu: typeof import('@mattverse/mattverse-ui')['NavigationMenu']
    NavigationMenuContent: typeof import('@mattverse/mattverse-ui')['NavigationMenuContent']
    NavigationMenuItem: typeof import('@mattverse/mattverse-ui')['NavigationMenuItem']
    NavigationMenuLink: typeof import('@mattverse/mattverse-ui')['NavigationMenuLink']
    NavigationMenuList: typeof import('@mattverse/mattverse-ui')['NavigationMenuList']
    NavigationMenuTrigger: typeof import('@mattverse/mattverse-ui')['NavigationMenuTrigger']
    NumberField: typeof import('@mattverse/mattverse-ui')['NumberField']
    NumberFieldContent: typeof import('@mattverse/mattverse-ui')['NumberFieldContent']
    NumberFieldDecrement: typeof import('@mattverse/mattverse-ui')['NumberFieldDecrement']
    NumberFieldIncrement: typeof import('@mattverse/mattverse-ui')['NumberFieldIncrement']
    NumberFieldInput: typeof import('@mattverse/mattverse-ui')['NumberFieldInput']
    Pagination: typeof import('@mattverse/mattverse-ui')['Pagination']
    PaginationContent: typeof import('@mattverse/mattverse-ui')['PaginationContent']
    PaginationEllipsis: typeof import('@mattverse/mattverse-ui')['PaginationEllipsis']
    PaginationItem: typeof import('@mattverse/mattverse-ui')['PaginationItem']
    PaginationNext: typeof import('@mattverse/mattverse-ui')['PaginationNext']
    PaginationPrevious: typeof import('@mattverse/mattverse-ui')['PaginationPrevious']
    PinInput: typeof import('@mattverse/mattverse-ui')['PinInput']
    PinInputGroup: typeof import('@mattverse/mattverse-ui')['PinInputGroup']
    PinInputSlot: typeof import('@mattverse/mattverse-ui')['PinInputSlot']
    Popover: typeof import('@mattverse/mattverse-ui')['Popover']
    PopoverContent: typeof import('@mattverse/mattverse-ui')['PopoverContent']
    PopoverTrigger: typeof import('@mattverse/mattverse-ui')['PopoverTrigger']
    Progress: typeof import('@mattverse/mattverse-ui')['Progress']
    ResizableHandle: typeof import('@mattverse/mattverse-ui')['ResizableHandle']
    ResizablePanel: typeof import('@mattverse/mattverse-ui')['ResizablePanel']
    ResizablePanelGroup: typeof import('@mattverse/mattverse-ui')['ResizablePanelGroup']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollArea: typeof import('@mattverse/mattverse-ui')['ScrollArea']
    Select: typeof import('@mattverse/mattverse-ui')['Select']
    SelectContent: typeof import('@mattverse/mattverse-ui')['SelectContent']
    SelectGroup: typeof import('@mattverse/mattverse-ui')['SelectGroup']
    SelectItem: typeof import('@mattverse/mattverse-ui')['SelectItem']
    SelectLabel: typeof import('@mattverse/mattverse-ui')['SelectLabel']
    SelectTrigger: typeof import('@mattverse/mattverse-ui')['SelectTrigger']
    SelectValue: typeof import('@mattverse/mattverse-ui')['SelectValue']
    Separator: typeof import('@mattverse/mattverse-ui')['Separator']
    Sheet: typeof import('@mattverse/mattverse-ui')['Sheet']
    SheetClose: typeof import('@mattverse/mattverse-ui')['SheetClose']
    SheetContent: typeof import('@mattverse/mattverse-ui')['SheetContent']
    SheetDescription: typeof import('@mattverse/mattverse-ui')['SheetDescription']
    SheetFooter: typeof import('@mattverse/mattverse-ui')['SheetFooter']
    SheetHeader: typeof import('@mattverse/mattverse-ui')['SheetHeader']
    SheetTitle: typeof import('@mattverse/mattverse-ui')['SheetTitle']
    SheetTrigger: typeof import('@mattverse/mattverse-ui')['SheetTrigger']
    Skeleton: typeof import('@mattverse/mattverse-ui')['Skeleton']
    Slash: typeof import('lucide-vue-next')['Slash']
    Slider: typeof import('@mattverse/mattverse-ui')['Slider']
    Stepper: typeof import('@mattverse/mattverse-ui')['Stepper']
    StepperDescription: typeof import('@mattverse/mattverse-ui')['StepperDescription']
    StepperIndicator: typeof import('@mattverse/mattverse-ui')['StepperIndicator']
    StepperItem: typeof import('@mattverse/mattverse-ui')['StepperItem']
    StepperSeparator: typeof import('@mattverse/mattverse-ui')['StepperSeparator']
    StepperTitle: typeof import('@mattverse/mattverse-ui')['StepperTitle']
    StepperTrigger: typeof import('@mattverse/mattverse-ui')['StepperTrigger']
    Switch: typeof import('@mattverse/mattverse-ui')['Switch']
    Table: typeof import('@mattverse/mattverse-ui')['Table']
    TableBody: typeof import('@mattverse/mattverse-ui')['TableBody']
    TableCaption: typeof import('@mattverse/mattverse-ui')['TableCaption']
    TableCell: typeof import('@mattverse/mattverse-ui')['TableCell']
    TableHead: typeof import('@mattverse/mattverse-ui')['TableHead']
    TableHeader: typeof import('@mattverse/mattverse-ui')['TableHeader']
    TableRow: typeof import('@mattverse/mattverse-ui')['TableRow']
    Tabs: typeof import('@mattverse/mattverse-ui')['Tabs']
    TabsContent: typeof import('@mattverse/mattverse-ui')['TabsContent']
    TabsList: typeof import('@mattverse/mattverse-ui')['TabsList']
    TabsTrigger: typeof import('@mattverse/mattverse-ui')['TabsTrigger']
    TagsInput: typeof import('@mattverse/mattverse-ui')['TagsInput']
    TagsInputInput: typeof import('@mattverse/mattverse-ui')['TagsInputInput']
    TagsInputItem: typeof import('@mattverse/mattverse-ui')['TagsInputItem']
    TagsInputItemDelete: typeof import('@mattverse/mattverse-ui')['TagsInputItemDelete']
    TagsInputItemText: typeof import('@mattverse/mattverse-ui')['TagsInputItemText']
    Textarea: typeof import('@mattverse/mattverse-ui')['Textarea']
    Toggle: typeof import('@mattverse/mattverse-ui')['Toggle']
    ToggleGroup: typeof import('@mattverse/mattverse-ui')['ToggleGroup']
    ToggleGroupItem: typeof import('@mattverse/mattverse-ui')['ToggleGroupItem']
    Tooltip: typeof import('@mattverse/mattverse-ui')['Tooltip']
    TooltipContent: typeof import('@mattverse/mattverse-ui')['TooltipContent']
    TooltipProvider: typeof import('@mattverse/mattverse-ui')['TooltipProvider']
    TooltipTrigger: typeof import('@mattverse/mattverse-ui')['TooltipTrigger']
  }
}
