import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useColorMode, useDark, useToggle } from '@vueuse/core'
import { createPersistConfig } from '@/store/plugins/persist-config'
import { setDocumentLang, type SupportedLocale } from '@mattverse/i18n'

/**
 * 主题模式类型
 */
export type ThemeMode = 'light' | 'dark' | 'system'

/**
 * 颜色主题类型
 */
export type ColorTheme =
  | 'city-light' | 'forest-light' | 'lake-light' | 'desert-light' | 'farm-light' | 'garden-light'
  | 'city-dark' | 'forest-dark' | 'lake-dark' | 'desert-dark' | 'farm-dark' | 'garden-dark'

/**
 * 语言代码类型
 */
export type LanguageCode = SupportedLocale

/**
 * 字体设置接口
 */
export interface FontSettings {
  fontFamily: string
  fontSize: number
}

/**
 * 缓存设置接口
 */
export interface CacheSettings {
  enableCache: boolean
  cacheSize: number // MB
  autoClearCache: boolean
  clearCacheOnExit: boolean
}

/**
 * 通知设置接口
 */
export interface NotificationSettings {
  enableNotifications: boolean
  desktopNotifications: boolean
  soundNotifications: boolean
  emailNotifications: boolean
}

/**
 * 高级设置接口
 */
export interface AdvancedSettings {
  debugMode: boolean
  autoSave: boolean
  backupFrequency: 'never' | 'daily' | 'weekly' | 'monthly'
  cacheSize: number // MB
  logLevel: 'error' | 'warn' | 'info' | 'debug'
}

/**
 * 设置状态接口
 */
export interface SettingsState {
  // 基本设置
  language: LanguageCode
  theme: ThemeMode
  colorTheme: ColorTheme

  // 字体设置
  font: FontSettings

  // 缓存设置
  cache: CacheSettings

  // 通知设置
  notifications: NotificationSettings

  // 高级设置
  advanced: AdvancedSettings

  // 首次启动标记
  isFirstLaunch: boolean
}

/**
 * 默认设置
 */
const defaultSettings: SettingsState = {
  language: 'zh-CN',
  theme: 'system',
  colorTheme: 'city-light',
  font: {
    fontFamily: '--font-sans',
    fontSize: 14,
  },
  cache: {
    enableCache: true,
    cacheSize: 100,
    autoClearCache: false,
    clearCacheOnExit: false,
  },
  notifications: {
    enableNotifications: true,
    desktopNotifications: true,
    soundNotifications: false,
    emailNotifications: false,
  },
  advanced: {
    debugMode: false,
    autoSave: true,
    backupFrequency: 'daily',
    cacheSize: 100,
    logLevel: 'info',
  },
  isFirstLaunch: true,
}

/**
 * 设置状态管理
 */
export const useSettingsStore = defineStore(
  'settings',
  () => {
    // 基本设置状态
    const language = ref<LanguageCode>(defaultSettings.language)
    const theme = ref<ThemeMode>(defaultSettings.theme)
    const colorTheme = ref<ColorTheme>(defaultSettings.colorTheme)

    // 字体设置状态
    const font = ref<FontSettings>({ ...defaultSettings.font })

    // 缓存设置状态
    const cache = ref<CacheSettings>({ ...defaultSettings.cache })

    // 通知设置状态
    const notifications = ref<NotificationSettings>({ ...defaultSettings.notifications })

    // 高级设置状态
    const advanced = ref<AdvancedSettings>({ ...defaultSettings.advanced })

    // 首次启动标记
    const isFirstLaunch = ref<boolean>(defaultSettings.isFirstLaunch)

    // 使用 VueUse 的主题管理
    const colorMode = useColorMode({
      initialValue: theme.value === 'system' ? 'auto' : theme.value,
    })

    const isDark = useDark({
      selector: 'html',
      attribute: 'class',
      valueDark: 'dark',
      valueLight: 'light',
    })

    const toggleDark = useToggle(isDark)

    // 计算属性
    const currentTheme = computed(() => {
      if (theme.value === 'system') {
        return isDark.value ? 'dark' : 'light'
      }
      return theme.value
    })

    const isSystemTheme = computed(() => theme.value === 'system')

    // 监听主题变化
    watch(
      theme,
      (newTheme) => {
        if (newTheme === 'system') {
          colorMode.value = 'auto'
        } else {
          colorMode.value = newTheme
          isDark.value = newTheme === 'dark'
        }
      },
      { immediate: true }
    )

    // 监听语言变化
    watch(
      language,
      (newLanguage) => {
        // 设置文档语言属性
        setDocumentLang(newLanguage)
      },
      { immediate: true }
    )

    // 方法
    const setLanguage = (newLanguage: LanguageCode) => {
      language.value = newLanguage
    }

    const setTheme = (newTheme: ThemeMode) => {
      theme.value = newTheme
    }

    const setColorTheme = (newColorTheme: ColorTheme) => {
      colorTheme.value = newColorTheme
      // 应用颜色主题到 DOM
      document.documentElement.setAttribute('data-theme', newColorTheme)
      
      // 应用对应的主题色彩 CSS 变量
      applyColorThemeVariables(newColorTheme)
    }

    // 应用主题色彩变量的函数
    const applyColorThemeVariables = (themeValue: ColorTheme) => {
      const root = document.documentElement
      
      // 主题色彩配置映射
      const colorThemeMap: Record<ColorTheme, string> = {
        'city-light': '187 25.9% 87.5%',
        'forest-light': '49 47.4% 78.2%',
        'lake-light': '243 17.3% 82.2%',
        'desert-light': '26 96.2% 84.1%',
        'farm-light': '222 47.4% 85.3%',
        'garden-light': '37 98.2% 85.1%',
        'city-dark': '197 22.9% 21.2%',
        'forest-dark': '79 22.4% 16.2%',
        'lake-dark': '220 42.4% 20.2%',
        'desert-dark': '26 32.2% 18.2%',
        'farm-dark': '69 22.4% 15.2%',
        'garden-dark': '315 22.4% 15.2%',
      }
      
      const primaryColor = colorThemeMap[themeValue]
      if (primaryColor) {
        // 设置主色调
        root.style.setProperty('--primary', primaryColor)
        
        // 根据主题类型设置前景色
        const isLight = themeValue.endsWith('-light')
        if (isLight) {
          // 浅色主题：深色前景
          const [h, s] = primaryColor.split(' ')
          root.style.setProperty('--primary-foreground', `${h} ${s} 20%`)
        } else {
          // 深色主题：浅色前景
          const [h, s] = primaryColor.split(' ')
          root.style.setProperty('--primary-foreground', `${h} ${s} 95%`)
        }
        
        console.log('Applied color theme:', {
          theme: themeValue,
          primary: primaryColor,
          foreground: root.style.getPropertyValue('--primary-foreground')
        })
      }
    }

    const toggleTheme = () => {
      if (theme.value === 'light') {
        setTheme('dark')
      } else if (theme.value === 'dark') {
        setTheme('system')
      } else {
        setTheme('light')
      }
    }

    const updateFont = (updates: Partial<FontSettings>) => {
      font.value = { ...font.value, ...updates }
    }

    const updateCache = (updates: Partial<CacheSettings>) => {
      cache.value = { ...cache.value, ...updates }
    }

    const updateNotifications = (updates: Partial<NotificationSettings>) => {
      notifications.value = { ...notifications.value, ...updates }
    }

    const updateAdvanced = (updates: Partial<AdvancedSettings>) => {
      advanced.value = { ...advanced.value, ...updates }
    }

    const resetToDefaults = () => {
      language.value = defaultSettings.language
      theme.value = defaultSettings.theme
      colorTheme.value = defaultSettings.colorTheme
      font.value = { ...defaultSettings.font }
      cache.value = { ...defaultSettings.cache }
      notifications.value = { ...defaultSettings.notifications }
      advanced.value = { ...defaultSettings.advanced }
    }

    const markAsLaunched = () => {
      isFirstLaunch.value = false
    }

    // 导出设置数据
    const exportSettings = () => {
      return {
        language: language.value,
        theme: theme.value,
        colorTheme: colorTheme.value,
        font: font.value,
        cache: cache.value,
        notifications: notifications.value,
        advanced: advanced.value,
        exportTime: new Date().toISOString(),
      }
    }

    // 导入设置数据
    const importSettings = (settingsData: Partial<SettingsState>) => {
      if (settingsData.language) {
        setLanguage(settingsData.language)
      }
      if (settingsData.theme) {
        setTheme(settingsData.theme)
      }
      if (settingsData.colorTheme) {
        setColorTheme(settingsData.colorTheme)
      }
      if (settingsData.font) {
        updateFont(settingsData.font)
      }
      if (settingsData.cache) {
        updateCache(settingsData.cache)
      }
      if (settingsData.notifications) {
        updateNotifications(settingsData.notifications)
      }
      if (settingsData.advanced) {
        updateAdvanced(settingsData.advanced)
      }
    }

    // 应用字体设置到全局的函数
    const applyFontSettings = (fontSettings: FontSettings) => {
      const root = document.documentElement
      
      // 获取字体的 CSS 变量和权重
      const getFontInfo = (fontValue: string) => {
        const fontMap: Record<string, { family: string; weight?: string }> = {
          'system-ui': { family: 'system-ui, -apple-system, sans-serif' },
          '--font-sans': { family: 'var(--font-sans)' },
          '--font-serif': { family: 'var(--font-serif)' },
          '--font-mono': { family: 'var(--font-mono)' },
          '--font-noto': { family: "'NotoSansSC', sans-serif", weight: '400' },
          '--font-noto-light': { family: "'NotoSansSC', sans-serif", weight: '300' },
          '--font-noto-regular': { family: "'NotoSansSC', sans-serif", weight: '400' },
          '--font-noto-medium': { family: "'NotoSansSC', sans-serif", weight: '500' },
          '--font-noto-extrabold': { family: "'NotoSansSC', sans-serif", weight: '800' },
          '--font-noto-black': { family: "'NotoSansSC', sans-serif", weight: '900' },
          '--font-alibaba-puhuiti': { family: "'AlibabaPuHuiTi', sans-serif" },
          '--font-alibaba-hk': { family: "'AlibabaSansHK', sans-serif" },
          '--font-source-han-sc': { family: "'SourceHanSansSC', sans-serif" },
          '--font-source-han-tc': { family: "'SourceHanSansTC', sans-serif" },
          '--font-source-han-hc': { family: "'SourceHanSansHC', sans-serif" },
          '--font-source-han-hk': { family: "'SourceHanSansHK', sans-serif" },
          '--font-source-han-k': { family: "'SourceHanSansK', sans-serif" },
        }
        
        return fontMap[fontValue] || { family: fontValue }
      }
      
      const fontInfo = getFontInfo(fontSettings.fontFamily)
      
      // 设置全局字体变量
      root.style.setProperty('--font-display', fontInfo.family)
      root.style.setProperty('--base-font-size', `${fontSettings.fontSize}px`)
      
      // 更新 body 样式
      if (document.body) {
        document.body.style.fontFamily = fontInfo.family
        document.body.style.fontSize = `${fontSettings.fontSize}px`
        if (fontInfo.weight) {
          document.body.style.fontWeight = fontInfo.weight
        }
      }
      
      // 添加调试信息
      console.log('Applied font settings:', {
        fontFamily: fontInfo.family,
        fontSize: `${fontSettings.fontSize}px`,
        fontWeight: fontInfo.weight || 'normal'
      })
    }

    // 监听字体设置变化 - 在函数定义之后
    watch(
      font,
      (newFont) => {
        // 应用字体设置到全局
        applyFontSettings(newFont)
      },
      { deep: true, immediate: true }
    )

    // 监听颜色主题变化并应用到 DOM
    watch(
      colorTheme,
      (newColorTheme) => {
        document.documentElement.setAttribute('data-theme', newColorTheme)
        applyColorThemeVariables(newColorTheme)
      },
      { immediate: true }
    )

    return {
      // 状态
      language,
      theme,
      colorTheme,
      font,
      cache,
      notifications,
      advanced,
      isFirstLaunch,

      // VueUse 相关
      colorMode,
      isDark,
      toggleDark,

      // 计算属性
      currentTheme,
      isSystemTheme,

      // 方法
      setLanguage,
      setTheme,
      setColorTheme,
      toggleTheme,
      updateFont,
      updateCache,
      updateNotifications,
      updateAdvanced,
      resetToDefaults,
      markAsLaunched,
      exportSettings,
      importSettings,
      applyFontSettings,
      applyColorThemeVariables,
    }
  },
  {
    persist: createPersistConfig('settings'),
  }
)
