// Component related translations
export default {
  // Sidebar component
  sidebar: {
    toggle: 'Toggle Sidebar',
    collapse: 'Collapse Sidebar',
    expand: 'Expand Sidebar',
  },
  
  // Language selector
  language_selector: {
    title: 'Select Language',
    current: 'Current Language',
  },
  
  // Theme selector
  theme_selector: {
    title: 'Select Theme',
    light: 'Light Theme',
    dark: 'Dark Theme',
    system: 'Follow System',
  },
  
  // Table component
  table: {
    no_data: 'No data available',
    loading: 'Loading data...',
    total_items: 'Total {count} items',
    items_per_page: 'Items per page',
    page_of: 'Page {current} of {total}',
    first_page: 'First',
    last_page: 'Last',
    previous_page: 'Previous',
    next_page: 'Next',
  },
  
  // Form component
  form: {
    required: 'This field is required',
    invalid_email: 'Please enter a valid email address',
    invalid_url: 'Please enter a valid URL',
    min_length: 'Minimum {min} characters required',
    max_length: 'Maximum {max} characters allowed',
    password_mismatch: 'Passwords do not match',
  },
  
  // Dialog component
  dialog: {
    confirm_title: 'Confirm Action',
    confirm_message: 'Are you sure you want to perform this action?',
    delete_title: 'Confirm Delete',
    delete_message: 'This action cannot be undone. Are you sure you want to delete?',
  },
  
  // File upload component
  file_upload: {
    drag_drop: 'Drag and drop files here, or click to select files',
    select_file: 'Select File',
    file_too_large: 'File size exceeds limit',
    invalid_file_type: 'Unsupported file type',
    upload_success: 'File uploaded successfully',
    upload_failed: 'File upload failed',
  },
  
  // Search component
  search: {
    placeholder: 'Enter search keywords',
    no_results: 'No results found',
    results_count: 'Found {count} results',
  },
}
