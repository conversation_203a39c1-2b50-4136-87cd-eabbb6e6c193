/* Mattverse Monorepo 统一主题配置 - Tailwind CSS v4 */
@import './fonts.css';

/* ===== CSS 变量定义 (shadcn-vue 兼容) ===== */
:root {
  /* 基础颜色系统 - HSL 格式 (shadcn-vue 标准) */
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 0 0% 9%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;
  --accent: 0 0% 96.1%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 85%;
  --input: 0 0% 89.8%;
  --ring: 0 0% 3.9%;

  /* 图表颜色系统 */
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;

  /* 侧边栏样式 */
  --sidebar: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217.2 91.2% 59.8%;

  /* 圆角系统 */
  --radius: 0.5rem;
}

.dark {
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 20%;
  --input: 0 0% 14.9%;
  --ring: 0 0% 83.1%;

  /* 暗色模式下的图表颜色 */
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;

  /* 侧边栏样式 */
  --sidebar: 240 5.9% 10%;
  --sidebar-foreground: 240 4.8% 95.9%;
  --sidebar-primary: 224.3 76.3% 48%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 240 4.8% 95.9%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}

/* 添加自定义颜色变量以支持主题预览 */
:root {
  /* 扩展橙色调色板 */
  --color-orange-25: #fffbf5;
  --color-yellow-25: #fffef0;

  /* 扩展灰色调色板 */
  --color-gray-150: #f8f9fa;
  --color-slate-850: #1e293b;
}

/* ===== Tailwind v4 主题配置 ===== */
@theme {
  /* ===== 基础颜色系统 (Tailwind v4 格式) ===== */
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  /* 侧边栏颜色 */
  --color-sidebar: hsl(var(--sidebar));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-ring: hsl(var(--sidebar-ring));

  /* ===== 图表颜色系统 ===== */
  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  /* ===== 可视化组件颜色 (来自旧配置) ===== */
  --color-vis-primary: var(--color-primary);
  --color-vis-secondary: 160 81% 40%;
  --color-vis-text: var(--color-muted-foreground);

  /* ===== 圆角系统 ===== */
  --radius: 0.5rem;
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;

  /* 容器配置 */
  --container-center: true;
  --container-padding: 2rem;
  --container-screens-2xl: 1400px;

  /* 断点 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* 自定义断点 */
  --breakpoint-workflow: 1200px;
  --breakpoint-dashboard: 1400px;
  --breakpoint-3xl: 1920px;

  /* ===== 字体系统 ===== */
  --font-sans:
    'NotoSansSC', 'SourceHanSansSC', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
    monospace;

  /* 扩展字体族 - 与实际字体文件保持一致 */
  --font-alibaba-puhuiti: 'AlibabaPuHuiTi', sans-serif;
  --font-alibaba-hk: 'AlibabaSansHK', sans-serif;
  --font-noto: 'NotoSansSC', sans-serif;
  --font-noto-light: 'NotoSansSC', sans-serif;
  --font-noto-regular: 'NotoSansSC', sans-serif;
  --font-noto-medium: 'NotoSansSC', sans-serif;
  --font-noto-extrabold: 'NotoSansSC', sans-serif;
  --font-noto-black: 'NotoSansSC', sans-serif;
  --font-source-han: 'SourceHanSans', sans-serif;
  --font-source-han-sc: 'SourceHanSansSC', sans-serif;
  --font-source-han-tc: 'SourceHanSansTC', sans-serif;
  --font-source-han-hc: 'SourceHanSansHC', sans-serif;
  --font-source-han-hk: 'SourceHanSansHK', sans-serif;
  --font-source-han-k: 'SourceHanSansK', sans-serif;
  --font-display: 'NotoSansSC', var(--font-sans);

  /* 阴影 */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  /* 模糊 */
  --blur-xs: 2px;
  --blur-sm: 4px;
  --blur-md: 8px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;

  /* ===== 网格模板系统 ===== */
  --grid-template-columns-workflow: 280px 1fr 320px;
  --grid-template-columns-dashboard: repeat(auto-fit, minmax(300px, 1fr));
  --grid-template-columns-simple-layout: 1fr 300px;
  --grid-template-columns-chart-grid: repeat(auto-fit, minmax(400px, 1fr));

  /* ===== 动画系统 ===== */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-collapsible-down: collapsible-down 0.2s ease-in-out;
  --animate-collapsible-up: collapsible-up 0.2s ease-in-out;
  --animate-ellipsis: ellipsis 1.2s infinite;
  --animate-highlight-pulse: highlight-pulse 2.5s ease-in-out infinite;

  /* ===== 滚动条样式 ===== */
  --scrollbar-width: 6px;
  --scrollbar-track-bg: 243 244 246;
  --scrollbar-thumb-bg: 209 213 219;
  --scrollbar-thumb-hover-bg: 156 163 175;

  /* ===== 侧边栏样式 ===== */
  --sidebar: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}

/* ===== 主题变体 ===== */

/* 浅色主题变体 */
[data-theme='city-light'] {
  --primary: 187 25.9% 87.5%;
  --primary-foreground: 187 25.9% 20%;
  --secondary: 187 25.9% 95%;
  --accent: 187 25.9% 90%;
}

[data-theme='forest-light'] {
  --primary: 49 47.4% 78.2%;
  --primary-foreground: 49 47.4% 20%;
  --secondary: 49 47.4% 95%;
  --accent: 49 47.4% 90%;
}

[data-theme='lake-light'] {
  --primary: 243 17.3% 82.2%;
  --primary-foreground: 243 17.3% 20%;
  --secondary: 243 17.3% 95%;
  --accent: 243 17.3% 90%;
}

[data-theme='desert-light'] {
  --primary: 26 96.2% 84.1%;
  --primary-foreground: 26 96.2% 20%;
  --secondary: 26 96.2% 95%;
  --accent: 26 96.2% 90%;
}

[data-theme='farm-light'] {
  --primary: 222 47.4% 85.3%;
  --primary-foreground: 222 47.4% 20%;
  --secondary: 222 47.4% 95%;
  --accent: 222 47.4% 90%;
}

[data-theme='garden-light'] {
  --primary: 37 98.2% 85.1%;
  --primary-foreground: 37 98.2% 20%;
  --secondary: 37 98.2% 95%;
  --accent: 37 98.2% 90%;
}

/* 深色主题变体 */
[data-theme='city-dark'] {
  --background: 197 22.9% 8%;
  --foreground: 197 22.9% 95%;
  --card: 197 22.9% 10%;
  --card-foreground: 197 22.9% 95%;
  --primary: 197 22.9% 21.2%;
  --primary-foreground: 197 22.9% 95%;
  --secondary: 197 22.9% 15%;
  --accent: 197 22.9% 18%;
  --border: 197 22.9% 25%;
  --input: 197 22.9% 20%;
}

[data-theme='forest-dark'] {
  --background: 79 22.4% 8%;
  --foreground: 79 22.4% 95%;
  --card: 79 22.4% 10%;
  --card-foreground: 79 22.4% 95%;
  --primary: 79 22.4% 16.2%;
  --primary-foreground: 79 22.4% 95%;
  --secondary: 79 22.4% 15%;
  --accent: 79 22.4% 18%;
  --border: 79 22.4% 25%;
  --input: 79 22.4% 20%;
}

[data-theme='lake-dark'] {
  --background: 220 42.4% 8%;
  --foreground: 220 42.4% 95%;
  --card: 220 42.4% 10%;
  --card-foreground: 220 42.4% 95%;
  --primary: 220 42.4% 20.2%;
  --primary-foreground: 220 42.4% 95%;
  --secondary: 220 42.4% 15%;
  --accent: 220 42.4% 18%;
  --border: 220 42.4% 25%;
  --input: 220 42.4% 20%;
}

[data-theme='desert-dark'] {
  --background: 26 32.2% 8%;
  --foreground: 26 32.2% 95%;
  --card: 26 32.2% 10%;
  --card-foreground: 26 32.2% 95%;
  --primary: 26 32.2% 18.2%;
  --primary-foreground: 26 32.2% 95%;
  --secondary: 26 32.2% 15%;
  --accent: 26 32.2% 18%;
  --border: 26 32.2% 25%;
  --input: 26 32.2% 20%;
}

[data-theme='farm-dark'] {
  --background: 69 22.4% 8%;
  --foreground: 69 22.4% 95%;
  --card: 69 22.4% 10%;
  --card-foreground: 69 22.4% 95%;
  --primary: 69 22.4% 15.2%;
  --primary-foreground: 69 22.4% 95%;
  --secondary: 69 22.4% 15%;
  --accent: 69 22.4% 18%;
  --border: 69 22.4% 25%;
  --input: 69 22.4% 20%;
}

[data-theme='garden-dark'] {
  --background: 315 22.4% 8%;
  --foreground: 315 22.4% 95%;
  --card: 315 22.4% 10%;
  --card-foreground: 315 22.4% 95%;
  --primary: 315 22.4% 15.2%;
  --primary-foreground: 315 22.4% 95%;
  --secondary: 315 22.4% 15%;
  --accent: 315 22.4% 18%;
  --border: 315 22.4% 25%;
  --input: 315 22.4% 20%;
}

/* ===== 动画关键帧定义 ===== */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--reka-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--reka-accordion-content-height);
  }
  to {
    height: 0;
  }
}

@keyframes collapsible-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes collapsible-up {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
  }
}

@keyframes ellipsis {
  0% {
    width: 0;
  }
  50% {
    width: 0.75rem;
  }
  100% {
    width: 0;
  }
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 224, 130, 0.3);
    transform: scale(1);
    border: 2.5px dashed #ffd600;
    background-color: #fffde7;
  }
  50% {
    box-shadow: 0 0 0 8px rgba(255, 224, 130, 0.2);
    transform: scale(1.02);
    border: 2.5px dashed #ffd600;
    background-color: #fff9c4;
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 224, 130, 0.3);
    transform: scale(1);
    border: 2.5px dashed #ffd600;
    background-color: #fffde7;
  }
}

/* ===== 基础样式 ===== */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--color-background));
  color: hsl(var(--color-foreground));
  font-feature-settings:
    'rlig' 1,
    'calt' 1;
  font-family: var(--font-display);
}

/* ===== 滚动条样式 ===== */
.scrollbar::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
}

.scrollbar::-webkit-scrollbar-track {
  background-color: rgb(var(--scrollbar-track-bg));
}

.scrollbar::-webkit-scrollbar-thumb {
  background-color: rgb(var(--scrollbar-thumb-bg));
  border-radius: 0.125rem;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgb(var(--scrollbar-thumb-hover-bg));
}

/* ===== 滚动条工具类 ===== */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background-color: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: hsl(var(--color-border));
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--color-muted-foreground));
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

.scrollbar-none {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* ===== 文本省略工具类 ===== */
.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* ===== 居中布局工具类 ===== */
.center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-x {
  display: flex;
  justify-content: center;
}

.center-y {
  display: flex;
  align-items: center;
}

/* ===== 安全区域工具类 ===== */
.safe-top {
  padding-top: env(safe-area-inset-top);
}

.safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-left {
  padding-left: env(safe-area-inset-left);
}

.safe-right {
  padding-right: env(safe-area-inset-right);
}

/* ===== 动画工具类 ===== */
.animate-accordion-down {
  animation: var(--animate-accordion-down);
}

.animate-accordion-up {
  animation: var(--animate-accordion-up);
}

.animate-collapsible-down {
  animation: var(--animate-collapsible-down);
}

.animate-collapsible-up {
  animation: var(--animate-collapsible-up);
}

.animate-ellipsis {
  animation: var(--animate-ellipsis);
}

.animate-highlight-pulse {
  animation: var(--animate-highlight-pulse);
}

/* ===== 高亮节点动画 ===== */
.highlight-node-animation {
  animation: highlight-pulse 2.5s ease-in-out infinite;
}

/* ===== 字体权重支持类 ===== */
.font-noto-light {
  font-family: 'NotoSansSC', sans-serif;
  font-weight: 300;
}

.font-noto-regular {
  font-family: 'NotoSansSC', sans-serif;
  font-weight: 400;
}

.font-noto-medium {
  font-family: 'NotoSansSC', sans-serif;
  font-weight: 500;
}

.font-noto-extrabold {
  font-family: 'NotoSansSC', sans-serif;
  font-weight: 800;
}

.font-noto-black {
  font-family: 'NotoSansSC', sans-serif;
  font-weight: 900;
}

.font-alibaba-puhuiti {
  font-family: 'AlibabaPuHuiTi', sans-serif;
  font-weight: normal;
}

.font-alibaba-hk {
  font-family: 'AlibabaSansHK', sans-serif;
  font-weight: normal;
}

.font-source-han-sc {
  font-family: 'SourceHanSansSC', sans-serif;
  font-weight: normal;
}

.font-source-han-tc {
  font-family: 'SourceHanSansTC', sans-serif;
  font-weight: normal;
}

.font-source-han-hc {
  font-family: 'SourceHanSansHC', sans-serif;
  font-weight: normal;
}

.font-source-han-hk {
  font-family: 'SourceHanSansHK', sans-serif;
  font-weight: normal;
}

.font-source-han-k {
  font-family: 'SourceHanSansK', sans-serif;
  font-weight: normal;
}

/* ===== 通用页面布局样式 ===== */

/* 主页面布局容器 - 上中下三段式布局 */
.page-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 3rem); /* 减去 padding */
  padding: 1rem;
  gap: 1rem;
}

/* 搜索表单区域 */
.search-section {
  flex-shrink: 0;
  /* 固定高度，不会被压缩 */
}

/* 表格区域 */
.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 占据剩余空间，可以滚动 */
  min-height: 0; /* 重要：允许 flex 子项收缩 */
}

/* 表格内部布局优化 */
.table-section .matt-table-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-section .matt-table-wrapper .matt-table-header {
  flex-shrink: 0;
}

.table-section .matt-table-wrapper .relative.w-full.overflow-auto {
  flex: 1;
  min-height: 0; /* 重要：允许表格内容滚动 */
}

/* 分页区域 */
.pagination-section {
  flex-shrink: 0;
  /* 固定高度，不会被压缩 */
  padding-top: 0.5rem;
  border-top: 1px solid hsl(var(--border));
}

/* 内容区域 - 通用内容容器 */
.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

/* 工具栏区域 */
.toolbar-section {
  flex-shrink: 0;
  padding: 0.5rem 0;
  border-bottom: 1px solid hsl(var(--border));
}

/* 侧边栏布局 */
.sidebar-layout {
  display: flex;
  height: 100%;
  min-height: calc(100vh - 3rem);
}

.sidebar-layout .sidebar {
  flex-shrink: 0;
  width: 280px;
  border-right: 1px solid hsl(var(--border));
}

.sidebar-layout .main-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .page-layout {
    padding: 0.5rem;
    gap: 0.75rem;
    min-height: calc(100vh - 1rem);
  }

  .pagination-section {
    padding-top: 0.25rem;
  }

  .sidebar-layout {
    flex-direction: column;
    min-height: calc(100vh - 1rem);
  }

  .sidebar-layout .sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid hsl(var(--border));
  }
}

@media (max-width: 640px) {
  .page-layout {
    padding: 0.25rem;
    gap: 0.5rem;
    min-height: calc(100vh - 0.5rem);
  }
}

/* 高度适配 */
@media (max-height: 600px) {
  .page-layout {
    min-height: 100vh;
  }

  .table-section {
    min-height: 300px; /* 确保表格有最小高度 */
  }

  .content-section {
    min-height: 200px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1600px) {
  .page-layout {
    padding: 1.5rem;
    gap: 1.5rem;
  }
}

/* 超宽屏优化 */
@media (min-width: 1920px) {
  .page-layout {
    max-width: 1800px;
    margin: 0 auto;
  }

  .sidebar-layout {
    max-width: 1800px;
    margin: 0 auto;
  }
}
