#!/usr/bin/env node

/**
 * 创建新应用脚本 - 支持交互式和命令行模式
 * 使用方法: 
 *   交互式: node scripts/generators/create-app.js
 *   命令行: node scripts/generators/create-app.js <app-name> [description]
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import readline from 'readline'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 创建交互式界面
function createInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    terminal: true
  })
}

// 询问用户输入
function askQuestion(rl, question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim())
    })
  })
}

// 替换模板变量
function replaceVariables(content, variables) {
  let result = content
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{${key}}}`, 'g')
    result = result.replace(regex, value)
  }
  return result
}

// 复制目录
function copyDirectory(src, dest, variables) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true })
  }

  const files = fs.readdirSync(src)
  
  for (const file of files) {
    const srcPath = path.join(src, file)
    const destPath = path.join(dest, file)
    
    const stat = fs.statSync(srcPath)
    
    if (stat.isDirectory()) {
      copyDirectory(srcPath, destPath, variables)
    } else {
      let content = fs.readFileSync(srcPath, 'utf-8')
      content = replaceVariables(content, variables)
      fs.writeFileSync(destPath, content)
    }
  }
}

// 验证应用名称
function validateAppName(name) {
  if (!name) {
    return '应用名称不能为空'
  }
  
  if (!/^[a-z0-9-]+$/.test(name)) {
    return '应用名称只能包含小写字母、数字和连字符'
  }
  
  if (name.length < 2) {
    return '应用名称至少需要2个字符'
  }
  
  return null
}

// 交互式模式
async function interactiveMode() {
  const rl = createInterface()
  
  console.log('🚀 Mattverse 应用创建器')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log('')
  
  let appName = ''
  let description = ''
  
  // 获取应用名称
  while (true) {
    appName = await askQuestion(rl, '📝 请输入应用名称 (例如: my-app): ')
    const error = validateAppName(appName)
    if (error) {
      console.log(`❌ ${error}`)
      continue
    }
    
    // 检查是否已存在
    const appDir = path.resolve(process.cwd(), 'apps', appName)
    if (fs.existsSync(appDir)) {
      console.log(`❌ 应用 ${appName} 已存在`)
      continue
    }
    
    break
  }
  
  // 获取描述
  description = await askQuestion(rl, '📄 请输入应用描述 (可选): ')
  if (!description) {
    description = `${appName} - Electron 桌面应用`
  }
  
  console.log('')
  console.log('📋 应用信息确认:')
  console.log(`   名称: ${appName}`)
  console.log(`   描述: ${description}`)
  console.log(`   路径: apps/${appName}`)
  console.log('')
  
  const confirm = await askQuestion(rl, '✅ 确认创建? (y/N): ')
  
  rl.close()
  
  if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
    console.log('❌ 已取消创建')
    return
  }
  
  return { appName, description }
}

// 创建应用
function createApp(appName, description) {
  console.log(`🚀 正在创建应用: ${appName}`)
  
  const appDir = path.resolve(process.cwd(), 'apps', appName)
  const templateDir = path.resolve(__dirname, '..', '..', 'templates', 'app-template')
  
  if (!fs.existsSync(templateDir)) {
    console.error('❌ 应用模板不存在')
    process.exit(1)
  }
  
  const variables = {
    APP_NAME: appName,
    DESCRIPTION: description,
  }
  
  copyDirectory(templateDir, appDir, variables)
  
  console.log('')
  console.log('✅ 应用创建成功!')
  console.log(`📁 应用目录: apps/${appName}`)
  console.log('')
  console.log('🔧 下一步操作:')
  console.log(`   cd apps/${appName}`)
  console.log(`   pnpm install`)
  console.log(`   pnpm dev`)
  console.log('')
}

// 主函数
async function main() {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    // 交互式模式
    const result = await interactiveMode()
    if (result) {
      createApp(result.appName, result.description)
    }
  } else {
    // 命令行模式
    const appName = args[0]
    const description = args[1] || `${appName} - Electron 桌面应用`
    
    const error = validateAppName(appName)
    if (error) {
      console.error(`❌ ${error}`)
      process.exit(1)
    }
    
    const appDir = path.resolve(process.cwd(), 'apps', appName)
    if (fs.existsSync(appDir)) {
      console.error(`❌ 应用 ${appName} 已存在`)
      process.exit(1)
    }
    
    createApp(appName, description)
  }
}

main().catch(console.error)
