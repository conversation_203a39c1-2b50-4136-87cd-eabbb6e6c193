/**
 * 日志工具
 */
import { app } from 'electron'
import fs from 'fs'
import path from 'path'

export type LogLevel = 'error' | 'warn' | 'info' | 'verbose' | 'debug' | 'silly'

// 检查是否在主进程中
const isMainProcess = typeof process !== 'undefined' && process.type === 'browser'

// 控制台颜色定义
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  gray: '\x1b[90m'
}

// 格式化控制台日志
function formatConsoleLog(level: string, message: string, ...args: unknown[]): void {
  const timestamp = new Date().toLocaleTimeString()
  let color = colors.white
  let levelText = level.toUpperCase()
  
  switch (level) {
    case 'error':
      color = colors.red
      levelText = '❌ ERROR'
      break
    case 'warn':
      color = colors.yellow
      levelText = '⚠️  WARN '
      break
    case 'info':
      color = colors.green
      levelText = 'ℹ️  INFO '
      break
    case 'debug':
      color = colors.blue
      levelText = '🔍 DEBUG'
      break
    case 'verbose':
      color = colors.gray
      levelText = '📝 VERBOSE'
      break
  }
  
  const formattedMessage = `${colors.gray}[${timestamp}]${colors.reset} ${color}${levelText}${colors.reset} ${message}`
  // 使用原生 console.log 进行格式化输出（这是格式化函数本身，不需要替换）
  console.log(formattedMessage, ...args)
}

let log: any = null

/**
 * 初始化日志系统
 */
function initLogger(processType: string = 'main'): typeof log {
  if (!isMainProcess) {
    // 非主进程使用控制台日志
    return {
      info: console.info.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console),
      debug: console.debug.bind(console),
      verbose: console.log.bind(console),
      silly: console.log.bind(console)
    }
  }

  try {
    log = require('electron-log')
    
    // 获取日志目录
    const logsPath = getLogsPath()
    
    // 确保日志目录存在
    ensureLogDirectory(logsPath)
    
    // 配置日志文件路径 - 按日期分割
    log.transports.file.resolvePath = () => {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const dateStr = `${year}-${month}-${day}`
      
      return path.join(logsPath, `${processType}-${dateStr}.log`)
    }
    
    // 基础配置
    log.transports.console.level = 'debug'
    log.transports.console.format = '[{y}-{m}-{d} {h}:{i}:{s}] {level} {text}'
    log.transports.file.level = 'info'
    log.transports.file.maxSize = 10 * 1024 * 1024 // 10MB
    
    // 日志归档处理
    log.transports.file.archiveLog = (oldLog: any) => {
      try {
        const logFilePath = oldLog.path
        const dirName = path.dirname(logFilePath)
        const fileName = path.basename(logFilePath)
        const baseName = fileName.replace('.log', '')
        
        // 查找现有归档文件，确定新的序号
        const files = fs.readdirSync(dirName)
        const archiveFiles = files.filter(
          (f) => f.startsWith(`${baseName}.old.`) && f.endsWith('.log')
        )
        const nextNumber = archiveFiles.length + 1
        
        const newName = path.join(dirName, `${baseName}.old.${nextNumber}.log`)
        fs.renameSync(logFilePath, newName)
        return newName
      } catch (e) {
        // 使用原生 console.error，因为这是日志系统内部错误
        console.error('无法归档日志文件', e)
        return null
      }
    }
    
    // 记录初始化信息
    const logFilePath = log.transports.file.getFile().path
    // 使用原生 console.log 显示初始化信息（这是日志系统本身的启动信息）
    console.log(`✅ ${processType} 日志系统初始化完成`)
    console.log(`📁 日志文件路径: ${logFilePath}`)
    
    log.info(`${processType} 日志系统初始化完成`, {
      path: logFilePath,
      time: new Date().toISOString(),
    })
    
    return log
  } catch (error) {
    // 使用原生 console.warn，因为这是日志系统初始化失败的降级处理
    console.warn('Failed to load electron-log:', error)
    // 降级到控制台日志
    return {
      info: console.info.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console),
      debug: console.debug.bind(console),
      verbose: console.log.bind(console),
      silly: console.log.bind(console)
    }
  }
}

/**
 * 获取日志目录路径
 */
function getLogsPath(): string {
  // 开发环境优先使用 Monorepo 最外层根目录，便于查看日志
  const isDev = process.env.NODE_ENV === 'development' || process.env.ELECTRON_IS_DEV
  
  if (isDev) {
    // 开发环境：找到 Monorepo 根目录（包含 pnpm-workspace.yaml 的目录）
    let currentDir = process.cwd()
    
    // 向上查找，直到找到包含 pnpm-workspace.yaml 的目录
    while (currentDir !== path.dirname(currentDir)) {
      const workspaceFile = path.join(currentDir, 'pnpm-workspace.yaml')
      if (fs.existsSync(workspaceFile)) {
        return path.join(currentDir, 'logs')
      }
      currentDir = path.dirname(currentDir)
    }
    
    // 如果没找到 workspace 文件，使用当前工作目录
    return path.resolve(process.cwd(), 'logs')
  }
  
  // 生产环境：尝试使用应用数据目录
  try {
    if (app) {
      const userDataPath = app.getPath('userData')
      return path.join(userDataPath, 'logs')
    }
  } catch (error) {
    // 使用原生 console.warn，因为这是日志系统初始化阶段的警告
    console.warn('无法获取应用数据路径:', error)
  }
  
  // 最终降级到项目根目录
  return path.resolve(process.cwd(), 'logs')
}

/**
 * 确保日志目录存在
 */
function ensureLogDirectory(logsPath: string): void {
  try {
    if (!fs.existsSync(logsPath)) {
      fs.mkdirSync(logsPath, { recursive: true })
      // 使用原生 console.log，因为这是日志系统初始化阶段的信息
      console.log(`日志目录已创建: ${logsPath}`)
    }
  } catch (error) {
    // 使用原生 console.error，因为这是日志系统初始化阶段的错误
    console.error('创建日志目录失败:', error)
    
    // 尝试使用临时目录
    try {
      const tempDir = app?.getPath('temp') || '/tmp'
      const tempLogsPath = path.join(tempDir, 'mattverse-logs')
      
      if (!fs.existsSync(tempLogsPath)) {
        fs.mkdirSync(tempLogsPath, { recursive: true })
      }
      console.log(`使用临时日志目录: ${tempLogsPath}`)
    } catch (tempError) {
      console.error('创建临时日志目录也失败:', tempError)
    }
  }
}

/**
 * 清理过期日志文件
 */
function cleanupOldLogs(): void {
  if (!isMainProcess) return

  try {
    const logsPath = getLogsPath()
    if (!fs.existsSync(logsPath)) return

    const files = fs.readdirSync(logsPath)
    const now = Date.now()
    const maxAge = 30 * 24 * 60 * 60 * 1000 // 30天（毫秒）
    let cleanedCount = 0

    files.forEach(file => {
      if (!file.endsWith('.log')) return

      const filePath = path.join(logsPath, file)
      try {
        const stats = fs.statSync(filePath)
        const fileAge = now - stats.mtime.getTime()

        // 删除超过30天的日志文件
        if (fileAge > maxAge) {
          fs.unlinkSync(filePath)
          cleanedCount++
          // 使用原生 console.log，因为这是日志清理的状态信息
          console.log(`🗑️  已清理过期日志: ${file}`)
        }
      } catch (error) {
        // 使用原生 console.warn，因为这是日志清理的警告信息
        console.warn(`清理日志文件失败: ${file}`, error)
      }
    })

    if (cleanedCount > 0) {
      // 使用原生 console.log，因为这是日志清理的完成信息
      console.log(`✅ 日志清理完成，共清理 ${cleanedCount} 个文件`)
    }
  } catch (error) {
    // 使用原生 console.error，因为这是日志清理的错误信息
    console.error('日志清理过程中出错:', error)
  }
}

/**
 * 启动定时清理任务
 */
function startLogCleanupScheduler(): void {
  if (!isMainProcess) return

  // 应用启动时立即执行一次清理
  setTimeout(() => {
    cleanupOldLogs()
  }, 5000) // 延迟5秒，确保应用完全启动

  // 每天凌晨2点执行清理（开发环境每小时执行一次）
  const isDev = process.env.NODE_ENV === 'development' || process.env.ELECTRON_IS_DEV
  const interval = isDev ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000 // 开发环境1小时，生产环境24小时

  setInterval(() => {
    const now = new Date()
    const hour = now.getHours()
    
    // 生产环境只在凌晨2点执行，开发环境每小时执行
    if (isDev || hour === 2) {
      cleanupOldLogs()
    }
  }, interval)

  // 使用原生 console.log，因为这是日志清理调度器的启动信息
  console.log(`🕒 日志清理调度器已启动 (${isDev ? '每小时' : '每天凌晨2点'}执行)`)
}

// 延迟初始化日志实例
function ensureLoggerInitialized() {
  if (!log && isMainProcess) {
    log = initLogger()
    // 启动日志清理调度器
    startLogCleanupScheduler()
  }
}

export const logger = {
  info: (message: string, ...args: unknown[]) => {
    ensureLoggerInitialized()
    if (log) {
      log.info(message, ...args)
    } else {
      formatConsoleLog('info', message, ...args)
    }
  },
  warn: (message: string, ...args: unknown[]) => {
    ensureLoggerInitialized()
    if (log) {
      log.warn(message, ...args)
    } else {
      formatConsoleLog('warn', message, ...args)
    }
  },
  error: (message: string, ...args: unknown[]) => {
    ensureLoggerInitialized()
    if (log) {
      log.error(message, ...args)
    } else {
      formatConsoleLog('error', message, ...args)
    }
  },
  debug: (message: string, ...args: unknown[]) => {
    ensureLoggerInitialized()
    if (log) {
      log.debug(message, ...args)
    } else {
      formatConsoleLog('debug', message, ...args)
    }
  },
  verbose: (message: string, ...args: unknown[]) => {
    ensureLoggerInitialized()
    if (log) {
      log.verbose(message, ...args)
    } else {
      formatConsoleLog('verbose', message, ...args)
    }
  },

  // 日志管理方法
  /**
   * 手动清理过期日志文件
   */
  cleanup: () => {
    cleanupOldLogs()
  },

  /**
   * 获取日志文件列表
   */
  getLogFiles: (): string[] => {
    try {
      const logsPath = getLogsPath()
      if (!fs.existsSync(logsPath)) return []
      
      return fs.readdirSync(logsPath)
        .filter(file => file.endsWith('.log'))
        .map(file => path.join(logsPath, file))
    } catch (error) {
      // 使用原生 console.error，因为这是日志管理功能的错误信息
      console.error('获取日志文件列表失败:', error)
      return []
    }
  },

  /**
   * 获取日志目录路径
   */
  getLogPath: (): string => {
    return getLogsPath()
  },

  /**
   * 获取日志统计信息
   */
  getLogStats: (): { totalFiles: number; totalSize: number; oldestFile: string | null; newestFile: string | null } => {
    try {
      const logsPath = getLogsPath()
      if (!fs.existsSync(logsPath)) {
        return { totalFiles: 0, totalSize: 0, oldestFile: null, newestFile: null }
      }

      const files = fs.readdirSync(logsPath)
        .filter(file => file.endsWith('.log'))
        .map(file => {
          const filePath = path.join(logsPath, file)
          const stats = fs.statSync(filePath)
          return {
            name: file,
            path: filePath,
            size: stats.size,
            mtime: stats.mtime
          }
        })

      if (files.length === 0) {
        return { totalFiles: 0, totalSize: 0, oldestFile: null, newestFile: null }
      }

      const totalSize = files.reduce((sum, file) => sum + file.size, 0)
      const sortedByTime = files.sort((a, b) => a.mtime.getTime() - b.mtime.getTime())

      return {
        totalFiles: files.length,
        totalSize,
        oldestFile: sortedByTime[0].name,
        newestFile: sortedByTime[sortedByTime.length - 1].name
      }
    } catch (error) {
      // 使用原生 console.error，因为这是日志管理功能的错误信息
      console.error('获取日志统计信息失败:', error)
      return { totalFiles: 0, totalSize: 0, oldestFile: null, newestFile: null }
    }
  }
}

export default logger
