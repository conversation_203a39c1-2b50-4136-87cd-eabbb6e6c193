/**
 * gRPC 服务基类
 * 提供统一的服务调用接口
 */
import { convertToCamelCase, logger } from '../../../utils'
// import type { ResponseStatus } from '../../../types/grpc'
export type ResponseStatus = 'Success' | 'Failed'
export interface BaseResponse {
  status: ResponseStatus
  message: string
}

export class BaseService {
  /**
   * 基础 gRPC 方法调用
   * @param methodName gRPC 方法名
   * @param params 请求参数
   * @returns Promise<T>
   */
  protected async call<T>(methodName: string, params: any): Promise<T> {
    try {
      logger.info(`gRPC 调用 ${methodName} ✅请求参数::`, params)
      const result = await (window as any).electronAPI.invoke('grpc:call', methodName, params)

      if (result.success) {
        //  logger.info(`gRPC 调用 ${methodName} ✅响应参数::`, convertToCamelCase(result.data) as T)
        return convertToCamelCase(result.data) as T
      } else {
        throw new Error(result.error || `调用 ${methodName} 失败`)
      }
    } catch (error) {
      ;(window as any).logger?.error(`Error calling ${methodName}:`, error)
      throw error
    }
  }

  /**
   * 默认服务调用
   * @param serviceName 服务名称
   * @param serverId 服务器ID
   * @param isSave 是否保存
   * @param params 请求参数
   * @returns Promise<T>
   */
  protected async default<T>(
    serviceName: string,
    serverId: string,
    isSave: boolean,
    params: any,
  ): Promise<T> {
    const requestParams = {
      service_name: serviceName,
      server_id: serverId,
      is_save: isSave,
      ...params
    }
    return this.call<T>('defaultService', requestParams)
  }

  /**
   * 提交任务服务调用
   * @param serviceName 服务名称
   * @param serverId 服务器ID
   * @param isSave 是否保存
   * @param params 请求参数
   * @returns Promise<T>
   */
  protected async submit<T>(
    serviceName: string,
    serverId: string,
    isSave: boolean,
    params: any,
  ): Promise<T> {
    const requestParams = {
      service_name: serviceName,
      server_id: serverId,
      is_save: isSave,
      ...params
    }
    return this.call<T>('submitService', requestParams)
  }
}
