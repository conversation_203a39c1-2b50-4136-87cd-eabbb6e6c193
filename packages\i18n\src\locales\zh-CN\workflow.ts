// 工作流相关
export default {
  // 基础操作
  title: '工作流管理',
  create: '创建工作流',
  edit: '编辑工作流',
  delete: '删除工作流',
  duplicate: '复制工作流',
  run: '运行工作流',
  stop: '停止工作流',
  pause: '暂停工作流',
  resume: '恢复工作流',
  
  // 状态
  status: {
    idle: '空闲',
    running: '运行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    pending: '等待中',
  },
  
  // 节点相关
  node: {
    add: '添加节点',
    delete: '删除节点',
    edit: '编辑节点',
    connect: '连接节点',
    disconnect: '断开连接',
    configure: '配置节点',
  },
  
  // 属性面板
  properties: {
    name: '名称',
    description: '描述',
    type: '类型',
    input: '输入',
    output: '输出',
    parameters: '参数',
    settings: '设置',
  },
  
  // 执行相关
  execution: {
    start_time: '开始时间',
    end_time: '结束时间',
    duration: '执行时长',
    progress: '执行进度',
    logs: '执行日志',
    result: '执行结果',
  },
}
