// Mattverse Monorepo 共享配置导出
export const CONFIG_VERSION = '1.0.0'

export const DEFAULT_CONFIG = {
  version: CONFIG_VERSION,
  name: 'Mattverse Configs',
}

// 配置管理器导出
export {
  ConfigurationManager,
  PackageType,
  ConfigFactory,
  createTsConfig,
  createEslintConfig,
  createPackageScripts,
  getPackageType,
  validateConfig,
} from './managers'

// 构建配置导出
export {
  createTsupConfig as createBaseTsupConfig,
  packageConfigs,
  type TsupConfigOptions,
} from './managers'

// 为了向后兼容，保留原有导出
export { createTsupConfig as createTsupConfigFromManager } from './managers'


// Vite 配置导出（按需导入，避免副作用）
export { createBaseViteConfig, type ViteConfigOptions } from './vite/base'
export { createElectronConfig, electronPresets } from './vite/electron.base'
export { createViteConfig, vitePresets, type BuildViteConfigOptions } from './vite/vite.base'
export { createElectronConfig as createElectronBuildConfig, electronBuildPresets } from './electron/electron.base'

// Vite 插件导出
export {
  createAutoImportPlugins,
  autoImportPresets,
  createCopyProtosPlugin,
  copyProtosConfigs
} from './vite/plugins'

// Tailwind CSS 配置导出
export * from './tailwind'

// ESLint 配置导出
export { eslintConfig } from './eslint/index.js'

