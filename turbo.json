{"$schema": "https://turbo.build/schema.json", "globalDependencies": [".env", ".npmrc", "pnpm-workspace.yaml", "turbo.json"], "remoteCache": {"signature": true}, "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "out/**", "release/**"], "cache": true, "env": ["NODE_ENV", "BUILD_ID", "API_URL", "VITE_*", "TAILWIND_*", "VUE_*", "ELECTRON_*"], "inputs": ["src/**/*.{ts,tsx,vue,js,jsx}", "package.json", "tsconfig*.json", "vite.config.ts", "electron.vite.config.ts"], "persistent": false}, "build:styles": {"inputs": ["src/**/*.{css,scss,sass}", "tailwind.config.ts", "postcss.config.*", "components.json"], "outputs": ["dist/**/*.css", "dist/style.css"], "cache": true}, "dev": {"dependsOn": [], "cache": false, "persistent": true, "env": ["NODE_ENV", "PORT", "HOST", "VITE_*", "ELECTRON_*"]}, "lint": {"cache": true, "inputs": ["src/**", "eslint.config.js", "eslint.config.cjs"], "outputs": [".eslint<PERSON>che"], "env": ["NODE_ENV"]}, "lint:fix": {"cache": false, "inputs": ["src/**", "eslint.config.js", "eslint.config.cjs"], "outputs": [".eslint<PERSON>che"], "env": ["NODE_ENV"]}, "typecheck": {"dependsOn": ["^build"], "cache": true, "inputs": ["src/**", "tsconfig.json"], "outputs": [], "env": ["NODE_ENV"]}, "test": {"dependsOn": ["^build"], "cache": true, "inputs": ["src/**", "test/**", "vitest.config.ts"], "outputs": ["coverage/**", "test-results/**"], "env": ["NODE_ENV", "CI"]}, "test:watch": {"cache": false, "env": ["NODE_ENV"]}, "clean": {"cache": false}, "build:win": {"dependsOn": ["build"], "cache": true, "inputs": ["src/**", "package.json", "electron-builder.yml"], "outputs": ["dist/**", "out/**", "release/**"], "env": ["NODE_ENV", "BUILD_ID", "ELECTRON_BUILDER_CACHE_DIR", "ELECTRON_*"]}, "build:mac": {"dependsOn": ["build"], "cache": true, "inputs": ["src/**", "package.json", "electron-builder.yml"], "outputs": ["dist/**", "out/**", "release/**"], "env": ["NODE_ENV", "BUILD_ID", "ELECTRON_BUILDER_CACHE_DIR", "ELECTRON_*"]}, "build:linux": {"dependsOn": ["build"], "cache": true, "inputs": ["src/**", "package.json", "electron-builder.yml"], "outputs": ["dist/**", "out/**", "release/**"], "env": ["NODE_ENV", "BUILD_ID", "ELECTRON_BUILDER_CACHE_DIR", "ELECTRON_*"]}, "preview": {"dependsOn": ["build"], "cache": false, "env": ["NODE_ENV", "PORT", "HOST", "VITE_*"]}, "format": {"cache": true, "inputs": ["src/**", "prettier.config.js"], "outputs": [], "env": ["NODE_ENV"]}}}