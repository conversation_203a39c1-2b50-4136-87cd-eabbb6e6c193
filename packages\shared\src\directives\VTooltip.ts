import { DirectiveBinding } from 'vue'
import tippy, { Props, Instance } from 'tippy.js'

// 扩展HTMLElement类型以包含_tippy属性
declare global {
  interface HTMLElement {
    _tippy?: Instance
  }
}

export default {
  mounted(el: HTMLElement, binding: DirectiveBinding<string | Props>) {
    // 处理指令的值，可以是字符串或配置对象
    const value = binding.value
    const options: Partial<Props> = {
      theme: 'light',
      animation: 'scale',
      placement: 'top',
      arrow: true,
      delay: [200, 0], // 显示延迟200ms，隐藏无延迟
      duration: [200, 150], // 显示动画200ms，隐藏动画150ms
      maxWidth: 300,
      interactive: false,
      hideOnClick: true,
    }

    // 如果传入的是字符串，则作为content
    if (typeof value === 'string') {
      options.content = value
    }
    // 如果传入的是对象，则合并配置
    else if (typeof value === 'object') {
      Object.assign(options, value)
    }

    // 创建tooltip
    tippy(el, options)
  },

  updated(el: HTMLElement, binding: DirectiveBinding<string | Props>) {
    // 获取tippy实例
    const instance = el._tippy
    if (instance) {
      const value = binding.value

      if (typeof value === 'string') {
        instance.setContent(value)
      } else if (typeof value === 'object') {
        instance.setProps(value)
      }
    }
  },

  beforeUnmount(el: HTMLElement) {
    // 销毁tippy实例
    const instance = el._tippy
    if (instance) {
      instance.destroy()
    }
  },
}
