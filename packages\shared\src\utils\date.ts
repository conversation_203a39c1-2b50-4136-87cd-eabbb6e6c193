
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import {logger} from './renderer-logger'
/**
 * 格式化日期时间字符串
 * @param dateString ISO 格式的日期字符串或 Date 对象
 * @param format 'YYYY/MM/DD HH:mm:ss' | 'YYYY-MM-DD' | 'HH:mm:ss'
 * @returns 格式化后的字符串
 */
export function formatDate(
  dateString: string | Date,
  format: 'YYYY/MM/DD HH:mm:ss' | 'YYYY-MM-DD' | 'HH:mm:ss' = 'YYYY/MM/DD HH:mm:ss',
): string {
  try {
    const date = new Date(dateString)

    // 检查日期是否有效
    if (Number.isNaN(date.getTime())) {
      return ''
    }

    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')

    switch (format) {
      case 'YYYY/MM/DD HH:mm:ss':
        return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
      case 'YYYY-MM-DD':
        return `${year}-${month}-${day}`
      case 'HH:mm:ss':
        return `${hours}:${minutes}:${seconds}`
      default:
        return ''
    }
  } catch (error) {
    logger.error('Invalid date string provided to formatDate:', error)
    return ''
  }
}


// 计算持续时间
export const formatDuration = (startTime: number | string, endTime: number | string) => {
  // 转换为数字类型
  const start = typeof startTime === 'string' ? Number(startTime) : startTime
  let end = typeof endTime === 'string' ? Number(endTime) : endTime

  // 检查开始时间是否有效
  if (!start || start === 0) return '未开始'

  // 只有当结束时间为0时，才使用当前时间
  if (!end || end === 0) {
    end = Math.floor(Date.now() / 1000)
  }

  const durationMs = (end - start) * 1000
  if (durationMs < 0) return '0h 0m 0s'

  const hours = Math.floor(durationMs / (1000 * 60 * 60))
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((durationMs % (1000 * 60)) / 1000)

  return `${hours}h ${minutes}m ${seconds}s`
}


/**
 * 格式化时间为HH:mm格式
 * @param timestamp 时间戳（字符串或数字）
 * @returns 格式化后的时间字符串
 */
export const formatTime = (timestamp: string | number) => {
  try {
    const date = typeof timestamp === 'number' ? new Date(timestamp) : new Date(timestamp)
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch (error) {
    logger.error('时间格式化错误:', { error, timestamp })
    return '--:--'
  }
}


/**
 * 格式化相对日期（今天、昨天、X天前等）
 * @param date 日期（Date对象、字符串或数字）
 * @returns 格式化后的相对日期字符串
 */
export const formatRelativeDate = (date: Date | string | number) => {
  try {
    // 使用dayjs处理时间，支持多种输入格式
    let dayjsDate: dayjs.Dayjs
    if (typeof date === 'number') {
      dayjsDate = dayjs(date)
    } else if (typeof date === 'string') {
      // 如果是纯数字字符串，转换为数字
      const timestamp = /^\d+$/.test(date) ? parseInt(date) : date
      dayjsDate = dayjs(timestamp)
    } else {
      dayjsDate = dayjs(date)
    }

    // 检查日期是否有效
    if (!dayjsDate.isValid()) {
      return '未知时间'
    }

    const now = dayjs()
    const diffDays = now.diff(dayjsDate, 'day')

    if (diffDays === 0) return '今天'
    if (diffDays === 1) return '昨天'
    if (diffDays < 7) return `${diffDays}天前`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`

    return dayjsDate.format('YYYY-MM-DD')
  } catch (error) {
    logger.error('相对日期格式化错误:', { error, date })
    return '未知时间'
  }
}

/**
 * 格式化会话时间（用于侧边栏显示）
 * @param dateStr 日期字符串
 * @returns 格式化后的时间字符串
 */
export const formatSessionTime = (dateStr: string | number) => {
  try {
    // 使用dayjs处理时间
    let date: dayjs.Dayjs
    if (typeof dateStr === 'number') {
      date = dayjs(dateStr)
    } else if (typeof dateStr === 'string') {
      // 如果是纯数字字符串，转换为数字
      const timestamp = /^\d+$/.test(dateStr) ? parseInt(dateStr) : dateStr
      date = dayjs(timestamp)
    } else {
      throw new Error('Invalid date input')
    }

    // 检查日期是否有效
    if (!date.isValid()) {
      throw new Error('Invalid date')
    }

    const now = dayjs()

    // 今天内显示时间
    if (date.isSame(now, 'day')) {
      return date.format('HH:mm')
    }

    // 昨天
    if (date.isSame(now.subtract(1, 'day'), 'day')) {
      return '昨天'
    }

    // 一周内显示星期
    if (date.isAfter(now.subtract(7, 'day'))) {
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      return weekdays[date.day()]
    }

    // 更早显示日期
    return date.format('M月D日')
  } catch (error) {
    logger.error('会话时间格式化错误:', { error, dateStr })
    return '未知时间'
  }
}

/**
 * 格式化搜索结果时间
 * @param dateStr 日期字符串
 * @returns 格式化后的时间字符串
 */
export const formatSearchTime = (dateStr: string | number) => {
  try {
    // 使用dayjs处理时间
    let date: dayjs.Dayjs
    if (typeof dateStr === 'number') {
      date = dayjs(dateStr)
    } else if (typeof dateStr === 'string') {
      const timestamp = /^\d+$/.test(dateStr) ? parseInt(dateStr) : dateStr
      date = dayjs(timestamp)
    } else {
      throw new Error('Invalid date input')
    }

    if (!date.isValid()) {
      throw new Error('Invalid date')
    }

    const now = dayjs()

    if (date.isSame(now, 'day')) {
      return '今天'
    } else if (date.isSame(now.subtract(1, 'day'), 'day')) {
      return '昨天'
    } else if (date.isAfter(now.subtract(7, 'day'))) {
      return `${now.diff(date, 'day')}天前`
    } else {
      return date.format('YYYY/M/D')
    }
  } catch (error) {
    logger.error('搜索时间格式化错误:', { error, dateStr })
    return '未知时间'
  }
}
