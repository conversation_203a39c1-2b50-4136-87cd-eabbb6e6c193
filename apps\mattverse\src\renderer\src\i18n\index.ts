/**
 * Mattverse 应用国际化配置
 */
import { installI18n, getBrowserLocale, setDocumentLang, type SupportedLocale } from '@mattverse/i18n'
import type { App } from 'vue'

// 应用特有的语言包
import mattverseZhCN from './locales/zh-CN.json'
import mattverseEnUS from './locales/en-US.json'
import mattverseJaJP from './locales/ja-JP.json'

// 合并应用特有的语言包
const appMessages = {
  'zh-CN': mattverseZhCN,
  'en-US': mattverseEnUS,
  'ja-JP': mattverseJaJP,
}

// 从 localStorage 获取保存的语言设置
function getSavedLocale(): SupportedLocale | null {
  try {
    const saved = localStorage.getItem('mattverse-locale')
    return saved as SupportedLocale
  } catch {
    return null
  }
}

// 保存语言设置到 localStorage
function saveLocale(locale: SupportedLocale) {
  try {
    localStorage.setItem('mattverse-locale', locale)
  } catch {
    // 忽略存储错误
  }
}

// 获取初始语言设置
function getInitialLocale(): SupportedLocale {
  // 优先级：保存的设置 > 浏览器语言 > 默认语言
  return getSavedLocale() || getBrowserLocale()
}

// 安装国际化插件
export function setupI18n(app: App) {
  const initialLocale = getInitialLocale()
  
  const i18n = installI18n(app, {
    locale: initialLocale,
    messages: appMessages, // 应用特有的语言包会与共享包合并
  })

  // 设置文档语言属性
  setDocumentLang(initialLocale)

  // 监听语言变化
  const { locale } = i18n.global
  watch(locale, (newLocale: SupportedLocale) => {
    saveLocale(newLocale)
    setDocumentLang(newLocale)
  })

  return i18n
}

// 导出语言切换函数
export function switchLanguage(newLocale: SupportedLocale) {
  const { locale } = useI18n()
  locale.value = newLocale
}

// 导出当前语言获取函数
export function getCurrentLocale(): SupportedLocale {
  const { locale } = useI18n()
  return locale.value as SupportedLocale
}
