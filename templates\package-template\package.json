{"name": "@mattverse/{{PACKAGE_NAME}}", "version": "1.0.0", "private": true, "description": "{{DESCRIPTION}}", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@mattverse/shared": "workspace:*"}, "devDependencies": {"@mattverse/configs": "workspace:*"}}