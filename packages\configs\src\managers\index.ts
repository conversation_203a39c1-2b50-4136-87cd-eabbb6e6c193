/**
 * 配置管理器模块导出
 */
export * from './ConfigurationManager'
export * from './tsup.config'

import { ConfigurationManager, PackageType } from './ConfigurationManager'

/**
 * 配置工厂函数
 * 提供便捷的配置生成方法
 */
export class ConfigFactory {
  private static manager = ConfigurationManager.getInstance()

  /**
   * 为指定包生成 TypeScript 配置
   * @param packageName 包名
   * @param packagePath 包路径
   * @param customOptions 自定义选项
   * @returns TypeScript 配置对象
   */
  static createTsConfig(
    packageName: string,
    packagePath: string,
    customOptions: any = {}
  ) {
    const packageType = this.manager.identifyPackageType(packageName, packagePath)
    return this.manager.generateTsConfig(packageType, customOptions)
  }

  /**
   * 为指定包生成 Tsup 配置
   * @param packageName 包名
   * @param customOptions 自定义选项
   * @returns Tsup 配置对象
   */
  static createTsupConfig(packageName: string, customOptions: any = {}) {
    return this.manager.generateTsupConfig(packageName, customOptions)
  }

  /**
   * 为指定包生成 ESLint 配置
   * @param packageName 包名
   * @param packagePath 包路径
   * @param customOptions 自定义选项
   * @returns ESLint 配置数组
   */
  static createEslintConfig(
    packageName: string,
    packagePath: string,
    customOptions: any = {}
  ) {
    const packageType = this.manager.identifyPackageType(packageName, packagePath)
    return this.manager.generateEslintConfig(packageType, customOptions)
  }

  /**
   * 为指定包生成标准脚本
   * @param packageName 包名
   * @param packagePath 包路径
   * @returns 脚本对象
   */
  static createPackageScripts(packageName: string, packagePath: string) {
    const packageType = this.manager.identifyPackageType(packageName, packagePath)
    return this.manager.generatePackageScripts(packageType)
  }

  /**
   * 获取包类型
   * @param packageName 包名
   * @param packagePath 包路径
   * @returns 包类型
   */
  static getPackageType(packageName: string, packagePath: string): PackageType {
    return this.manager.identifyPackageType(packageName, packagePath)
  }

  /**
   * 验证配置
   * @param config 配置对象
   * @param configType 配置类型
   * @returns 验证结果
   */
  static validateConfig(config: any, configType: string) {
    return this.manager.validateConfig(config, configType)
  }
}

/**
 * 便捷的配置生成函数
 */

/**
 * 生成 TypeScript 配置
 */
export const createTsConfig = ConfigFactory.createTsConfig

/**
 * 生成 Tsup 配置
 */
export const createTsupConfig = ConfigFactory.createTsupConfig

/**
 * 生成 ESLint 配置
 */
export const createEslintConfig = ConfigFactory.createEslintConfig

/**
 * 生成包脚本
 */
export const createPackageScripts = ConfigFactory.createPackageScripts

/**
 * 获取包类型
 */
export const getPackageType = ConfigFactory.getPackageType

/**
 * 验证配置
 */
export const validateConfig = ConfigFactory.validateConfig
