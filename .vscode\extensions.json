{
  "recommendations": [
    // 代码格式化
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "editorconfig.editorconfig",

    // Vue 开发
    "vue.volar",
    "vue.vscode-typescript-vue-plugin",

    // TypeScript 支持
    "ms-vscode.vscode-typescript-next",

    // 实用工具
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",

    // Git 工具
    "eamodio.gitlens",

    // 主题和图标（可选）
    "pkief.material-icon-theme"
  ],
  "unwantedRecommendations": [
    // 避免冲突的格式化工具
    "hookyqr.beautify",
    "ms-vscode.vscode-json",
    "octref.vetur"
  ]
}
