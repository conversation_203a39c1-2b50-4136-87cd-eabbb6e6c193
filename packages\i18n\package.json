{"name": "@mattverse/i18n", "version": "1.0.0", "private": true, "description": "Mattverse 国际化共享包", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./locales/*": "./src/locales/*"}, "files": ["dist", "src/locales"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --noEmit"}, "dependencies": {"vue-i18n": "^10.0.0"}, "devDependencies": {"@mattverse/configs": "workspace:*"}, "peerDependencies": {"vue": "^3.5.0"}}