{"app": {"name": "Mattverse", "description": "AI-powered workflow automation platform", "welcome": "Welcome to Mattverse"}, "pages": {"workflow": {"description": "Create and manage your automated workflows", "empty": "No workflows yet, click to create your first workflow", "create_button": "Create Workflow"}, "task": {"description": "View and manage computing task status", "queue": "Task Queue"}, "server": {"description": "Monitor server status and performance metrics", "cpu_usage": "CPU Usage", "memory_usage": "Memory Usage", "disk_usage": "Disk Usage", "network": "Network Status"}, "logger": {"description": "View system logs and error information", "level": {"debug": "Debug", "info": "Info", "warn": "Warning", "error": "Error"}}, "tools": {"description": "Manage and configure various tool modules", "node": {"description": "Manage workflow node tools"}, "agent": {"description": "Configure and manage AI agent tools"}, "other": {"description": "Other utilities and plugins"}}, "settings": {"description": "Configure application settings and preferences", "basic": {"description": "Basic configuration options"}, "middleware": {"description": "Middleware service configuration"}, "flow": {"description": "Workflow related settings"}, "about": {"description": "Application information and version"}}}}