/**
 * IPC 通道类型定义
 * 提供类型安全的 IPC 通信
 */

export interface AppInfo {
  name: string
  version: string
  platform: string
  arch: string
}

export interface WindowState {
  isMaximized: boolean
  isMinimized: boolean
  isFullScreen: boolean
  bounds: {
    x: number
    y: number
    width: number
    height: number
  }
}

export interface FileDialogOptions {
  title?: string
  defaultPath?: string
  filters?: Array<{
    name: string
    extensions: string[]
  }>
  properties?: Array<'openFile' | 'openDirectory' | 'multiSelections' | 'showHiddenFiles'>
}

export interface SaveDialogOptions {
  title?: string
  defaultPath?: string
  filters?: Array<{
    name: string
    extensions: string[]
  }>
  properties?: Array<'showHiddenFiles' | 'createDirectory' | 'treatPackageAsDirectory' | 'dontAddToRecent' | 'showOverwriteConfirmation'>
}

/**
 * 基础 IPC 通道定义
 * 每个通道都有明确的输入输出类型
 */
export interface BaseIPCChannels {
  // App 相关
  'app:get-version': () => string
  'app:get-info': () => AppInfo
  'app:quit': () => void
  'app:restart': () => void

  // Window 相关
  'window:minimize': () => void
  'window:maximize': () => void
  'window:close': () => void
  'window:get-state': () => WindowState | null
  'window:set-size': (width: number, height: number) => void
  'window:center': () => void

  // Dialog 相关
  'dialog:select-file': (options?: FileDialogOptions) => Promise<string | null>
  'dialog:select-folder': () => Promise<string | null>
  'dialog:save-file': (options?: SaveDialogOptions) => Promise<string | null>
  'dialog:show-message': (message: string, type?: 'info' | 'warning' | 'error') => Promise<void>

  // Store 相关
  'store:get': (key: string) => any
  'store:set': (key: string, value: any) => void
  'store:delete': (key: string) => void
  'store:clear': () => void
  'store:has': (key: string) => boolean

  // File System 相关
  'fs:read-file': (path: string) => string | null
  'fs:write-file': (path: string, content: string) => boolean
  'fs:exists': (path: string) => boolean
  'fs:mkdir': (path: string) => boolean

  // Logger 相关
  'logger:info': (message: string, ...args: any[]) => void
  'logger:warn': (message: string, ...args: any[]) => void
  'logger:error': (message: string, ...args: any[]) => void
  'logger:debug': (message: string, ...args: any[]) => void
}

/**
 * 扩展的 IPC 通道定义，支持自定义通道
 */
export interface IPCChannels extends BaseIPCChannels {
  // 允许任意字符串作为通道名，支持自定义通道
  [channel: string]: (...args: any[]) => any
}

export type IPCChannelName = string
export type IPCChannelHandler = (...args: unknown[]) => unknown
export type IPCChannelParams = unknown[]
export type IPCChannelReturn = unknown
