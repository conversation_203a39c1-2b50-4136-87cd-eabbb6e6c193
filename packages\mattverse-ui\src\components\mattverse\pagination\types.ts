export interface MattPaginationProps {
  /** 当前页码 */
  current: number
  /** 每页条数 */
  pageSize: number
  /** 总条数 */
  total: number
  /** 每页条数选择器的选项 */
  pageSizeOptions?: number[]
  /** 是否显示每页条数选择器 */
  showSizeChanger?: boolean
  /** 是否显示快速跳转 */
  showQuickJumper?: boolean
  /** 是否显示总数 */
  showTotal?: boolean
  /** 总数显示模板 */
  totalTemplate?: (total: number, range: [number, number]) => string
  /** 是否显示较少页码 */
  showLessItems?: boolean
  /** 是否简单模式 */
  simple?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 组件大小 */
  size?: 'sm' | 'default' | 'lg'
  /** 自定义样式类 */
  class?: string
  /** 最多显示的页码数量 */
  maxPageItems?: number
}

export interface MattPaginationEmits {
  'update:current': [page: number]
  'update:pageSize': [pageSize: number]
  'change': [page: number, pageSize: number]
  'showSizeChange': [current: number, size: number]
}

export interface PageInfo {
  current: number
  pageSize: number
  total: number
  totalPages: number
  startIndex: number
  endIndex: number
  hasNext: boolean
  hasPrev: boolean
}
