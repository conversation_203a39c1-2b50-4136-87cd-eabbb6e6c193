<template>
  <div class="page-layout">
    <!-- 搜索表单区域 -->
    <div class="search-section">
      <MattSearchForm
        :fields="searchFields"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
      />
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <MattTable
        :columns="tableColumns"
        :data="paginatedData"
        :loading="loading"
        :actions="tableActions"
        :show-column-settings="true"
        :row-selection="rowSelection"
        :row-key="record => record.taskId"
        class="h-full"
        :max-height="650"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 表格左上角插槽：页面标题和选中信息 -->
        <template #header-left>
          <div class="flex items-center gap-4">
            <h1 class="text-2xl text-primary font-bold">计算任务</h1>
            <div
              v-if="rowSelection.selectedRowKeys.length > 0"
              class="text-sm text-muted-foreground"
            >
              已选择 {{ rowSelection.selectedRowKeys.length }} 个任务
            </div>
          </div>
        </template>

        <!-- 表格右上角插槽：批量操作按钮和刷新设置 -->
        <template #header-right>
          <div class="flex items-center gap-2">
            <!-- 批量操作按钮 -->
            <template v-if="rowSelection.selectedRowKeys.length > 0">
              <Button
                v-for="action in batchActions"
                :key="action.key"
                :variant="action.variant"
                size="sm"
                class="h-9 gap-2"
                @click="handleBatchAction(action.key)"
              >
                <MattIcon :name="action.icon" class="h-4 w-4" />
                {{ action.label }}
              </Button>
            </template>

            <!-- 刷新间隔设置 -->
            <Label class="text-sm text-muted-foreground whitespace-nowrap">刷新间隔（秒）</Label>
            <Input
              v-model.number="refreshInterval"
              type="number"
              min="1"
              max="300"
              class="w-20 h-9"
              @change="handleRefreshIntervalChange"
            />
            <Button
              variant="outline"
              size="sm"
              class="h-9 gap-2"
              @click="handleRefresh"
              :disabled="loading"
            >
              <MattIcon name="RefreshCw" class="h-4 w-4" />
              刷新
            </Button>
          </div>
        </template>

        <!-- 任务编号列 - 支持复制 -->
        <template #taskId="{ record }">
          <div class="flex items-center gap-2">
            <span class="font-mono text-xs">{{ record.taskId }}</span>
            <Button
              variant="ghost"
              size="icon"
              class="h-6 w-6"
              @click.stop="copyToClipboard(record.taskId)"
            >
              <MattIcon name="Copy" class="h-3 w-3" />
            </Button>
          </div>
        </template>

        <!-- 运行状态列 -->
        <template #taskStatus="{ record }">
          <Badge :class="getStatusClass(record.taskStatus)">
            {{ getStatusText(record.taskStatus) }}
          </Badge>
        </template>

        <!-- 提交时间列 -->
        <template #createTime="{ record }">
          <span class="text-sm">{{ formatTimestamp(record.createTime) }}</span>
        </template>

        <!-- 运行时间列 -->
        <template #duration="{ record }">
          <span class="text-sm">{{ record.duration || '-' }}</span>
        </template>

        <!-- 进度列 -->
        <template #taskProcess="{ record }">
          <div class="flex items-center gap-2 min-w-[120px]">
            <Progress :model-value="getValidProgress(record.taskProcess)" class="flex-1" />
            <span class="text-xs text-muted-foreground whitespace-nowrap">
              {{ (record.taskProcess || 0).toFixed(2) }}%
            </span>
          </div>
        </template>

        <!-- 结果列 -->
        <template #result="{ record }">
          <Button
            variant="ghost"
            size="sm"
            class="h-8 w-8 p-0"
            @click="() => openLogDetail(record)"
            title="查看详情"
          >
            <MattIcon name="FileText" class="h-4 w-4" />
          </Button>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
                <MattIcon name="MoreHorizontal" class="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" class="w-40">
              <template v-for="(action, index) in taskActions" :key="action.key">
                <!-- 分隔线 -->
                <DropdownMenuSeparator v-if="action.separator && index > 0" />

                <!-- 操作项 -->
                <DropdownMenuItem
                  @click="handleTaskActionClick(action.key, record)"
                  :disabled="isActionDisabled(action, record)"
                  :class="
                    action.variant === 'destructive'
                      ? 'text-destructive focus:text-destructive'
                      : ''
                  "
                >
                  <MattIcon :name="action.icon" class="mr-2 h-4 w-4" />
                  {{ action.label }}
                </DropdownMenuItem>
              </template>
            </DropdownMenuContent>
          </DropdownMenu>
        </template>
      </MattTable>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <MattPagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="sortedData.length"
        :show-size-changer="true"
        :show-quick-jumper="true"
        @change="handlePageChange"
      />
    </div>

    <!-- 删除确认弹框 -->
    <AlertDialog v-model:open="deleteDialogOpen">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{{ deleteDialogContent.title }}</AlertDialogTitle>
          <AlertDialogDescription>
            {{ deleteDialogContent.description }}
            <br />
            <span class="text-muted-foreground text-sm">
              {{ deleteDialogContent.detail }}
            </span>
            <br />
            <span class="text-destructive text-sm font-medium">
              {{ deleteDialogContent.warning }}
            </span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="closeDeleteDialog">取消</AlertDialogCancel>
          <AlertDialogAction
            @click="confirmDeleteTask"
            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            :disabled="deleteLoading"
          >
            <MattIcon v-if="deleteLoading" name="Loader2" class="mr-2 h-4 w-4 animate-spin" />
            {{ deleteDialogContent.confirmText }}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    <!-- 任务详情弹框 -->
    <LogDetail v-model:open="logDetailOpen" :task="selectedTaskForDetail" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, reactive } from 'vue'
import { toast } from 'vue-sonner'
import { useTaskStore } from '@/store'
import { logger, taskService, formatDate, type Task } from '@mattverse/shared'
import {
  MattSearchForm,
  MattTable,
  MattPagination,
  type MattTableColumn,
  MattTableAction,
  MattSearchFormField,
  Button,
  Badge,
  Progress,
  Label,
  Input,
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
  MattIcon,
} from '@mattverse/mattverse-ui'
import LogDetail from '@/components/dailog/LogDetail.vue'

// 使用 store
const taskStore = useTaskStore()

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const refreshInterval = ref(taskStore.refreshInterval)
const refreshTimer = ref<NodeJS.Timeout | null>(null)

// 删除相关状态
const deleteDialogOpen = ref(false)
const deleteLoading = ref(false)
const taskToDelete = ref<Task | null>(null)
const isBatchDelete = ref(false) // 是否为批量删除

// LogDetail 相关状态
const logDetailOpen = ref(false)
const selectedTaskForDetail = ref<Task | null>(null)

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox' as const,
  selectedRowKeys: [] as (string | number)[],
  onChange: (keys: (string | number)[], rows: Task[]) => {
    logger.info('选中的任务变化', { count: keys.length, rowCount: rows.length, keys })
    // 同步更新选中状态
    rowSelection.selectedRowKeys = keys
  },
})

// 排序状态
const currentSortField = ref<string | null>(null)
const currentSortOrder = ref<'asc' | 'desc' | null>(null)

// 批量操作按钮配置
const batchActions = computed(() => [
  {
    key: 'stop',
    label: '批量停止',
    icon: 'Square',
    variant: 'outline' as const,
  },
  {
    key: 'pause',
    label: '批量暂停',
    icon: 'Pause',
    variant: 'outline' as const,
  },
  {
    key: 'resume',
    label: '批量恢复',
    icon: 'Play',
    variant: 'outline' as const,
  },
  {
    key: 'delete',
    label: '批量删除',
    icon: 'Trash2',
    variant: 'destructive' as const,
  },
])

// 单个任务操作配置
const taskActions = computed(() => [
  {
    key: 'stop',
    label: '停止',
    icon: 'Square',
  },
  {
    key: 'pause',
    label: '暂停',
    icon: 'Pause',
  },
  {
    key: 'resume',
    label: '恢复',
    icon: 'Play',
  },
  {
    key: 'delete',
    label: '删除',
    icon: 'Trash2',
    variant: 'destructive' as const,
    separator: true, // 在此项前添加分隔线
  },
])

// 判断操作是否禁用
const isActionDisabled = (action: any, record: any): boolean => {
  const task = record as Task

  switch (action.key) {
    case 'stop':
      return (
        task.taskStatus === 'Finished' || task.taskStatus === 'Error' || task.taskStatus === 'Abort'
      )
    case 'pause':
      return (
        task.taskStatus === 'Finished' ||
        task.taskStatus === 'Error' ||
        task.taskStatus === 'Abort' ||
        task.taskStatus === 'Paused'
      )
    case 'resume':
      return task.taskStatus !== 'Paused'
    case 'delete':
      return false // 删除操作始终可用
    default:
      return false
  }
}

// 搜索表单数据
const searchForm = ref({
  keyword: '', // 搜索关键词（任务编号）
  taskStatus: 'all', // 任务状态筛选
})

// 搜索表单字段配置
const searchFields = computed<MattSearchFormField[]>(() => [
  {
    name: 'keyword',
    type: 'input',
    label: '搜索',
    placeholder: '搜索任务编号',
    icon: 'search',
    width: 'w-full sm:min-w-[280px] sm:max-w-[320px]',
  },
  {
    name: 'taskStatus',
    type: 'select',
    label: '运行状态',
    placeholder: '选择运行状态',
    options: [
      { label: '全部状态', value: 'all' },
      { label: '初始化中', value: 'Initializing' },
      { label: '计算中', value: 'Computing' },
      { label: '等待调度', value: 'Pending' },
      { label: '暂停', value: 'Paused' },
      { label: '任务完成', value: 'Finished' },
      { label: '任务失败', value: 'Error' },
      { label: '原始状态', value: 'TaskStay' },
      { label: '已终止', value: 'Abort' },
    ],
    width: 'w-full sm:min-w-[160px] sm:max-w-[200px]',
  },
])

// 表格列配置
const tableColumns = computed<MattTableColumn[]>(() => [
  {
    key: 'taskId',
    title: '任务编号',
    width: 200,
    slot: true,
    align: 'center',
    ellipsis: true,
    hideable: false,
    defaultVisible: true,
  },
  {
    key: 'taskStatus',
    title: '运行状态',
    width: 120,
    slot: true,
    align: 'center',
    hideable: false,
    defaultVisible: true,
  },
  {
    key: 'createTime',
    title: '提交时间',
    width: 150,
    slot: true,
    align: 'center',
    sortable: true,
    hideable: true,
    defaultVisible: true,
  },
  {
    key: 'duration',
    title: '运行时间',
    width: 120,
    slot: true,
    align: 'center',
    hideable: true,
    defaultVisible: true,
  },
  {
    key: 'taskProcess',
    title: '进度',
    width: 150,
    slot: true,
    align: 'center',
    hideable: false,
    defaultVisible: true,
  },
  {
    key: 'result',
    title: '结果',
    width: 80,
    slot: true,
    align: 'center',
    hideable: false,
    defaultVisible: true,
  },
])

// 表格操作配置
const tableActions = computed<MattTableAction[]>(() => [])

// 计算属性
const filteredData = computed(() => {
  let data = taskStore.tasks

  // 关键词搜索
  if (searchForm.value.keyword) {
    const keyword = searchForm.value.keyword.toLowerCase()
    data = data.filter(task => task.taskId.toLowerCase().includes(keyword))
  }

  // 任务状态筛选
  if (searchForm.value.taskStatus && searchForm.value.taskStatus !== 'all') {
    data = data.filter(task => task.taskStatus === searchForm.value.taskStatus)
  }

  return data
})

// 排序后的数据
const sortedData = computed(() => {
  const data = [...filteredData.value]

  // 如果有排序条件，进行排序
  if (currentSortField.value && currentSortOrder.value) {
    data.sort((a, b) => {
      let aValue: any = a[currentSortField.value as keyof Task]
      let bValue: any = b[currentSortField.value as keyof Task]

      // 处理时间字段的排序
      if (currentSortField.value === 'createTime') {
        // 将时间戳转换为数字进行比较
        aValue = Number(aValue) || 0
        bValue = Number(bValue) || 0
      }

      // 处理空值
      if (!aValue && !bValue) return 0
      if (!aValue) return 1
      if (!bValue) return -1

      // 比较值
      if (aValue < bValue) return currentSortOrder.value === 'asc' ? -1 : 1
      if (aValue > bValue) return currentSortOrder.value === 'asc' ? 1 : -1
      return 0
    })
  }

  return data
})

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return sortedData.value.slice(start, end)
})

// 删除弹框动态内容
const deleteDialogContent = computed(() => {
  if (isBatchDelete.value) {
    const selectedCount = rowSelection.selectedRowKeys.length
    return {
      title: '确认批量删除任务',
      description: `您确定要删除选中的 ${selectedCount} 个任务吗？`,
      detail: '此操作将删除所有选中的任务',
      warning: '此操作不可撤销，请谨慎操作！',
      confirmText: deleteLoading.value ? '删除中...' : '确认删除',
    }
  } else if (taskToDelete.value) {
    return {
      title: '确认删除任务',
      description: `您确定要删除任务 "${taskToDelete.value.taskId}" 吗？`,
      detail: `任务ID: ${taskToDelete.value.taskId}`,
      warning: '此操作不可撤销，请谨慎操作！',
      confirmText: deleteLoading.value ? '删除中...' : '确认删除',
    }
  }
  return {
    title: '确认删除',
    description: '',
    detail: '',
    warning: '',
    confirmText: '确认删除',
  }
})

// 工具方法
const { getStatusText, getStatusClass } = taskStore

// 获取有效的进度值（确保在0-100范围内）
const getValidProgress = (progress: number | undefined | null): number => {
  if (progress === null || progress === undefined || isNaN(progress)) {
    return 0
  }
  // 确保进度值在0-100范围内
  return Math.max(0, Math.min(100, progress))
}

// 格式化时间戳为日期时间字符串
const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return '-'

  // 将时间戳（秒）转换为毫秒
  const date = new Date(timestamp * 1000)

  return formatDate(date, 'YYYY/MM/DD HH:mm:ss')
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success(`已复制: ${text}`)
  } catch (err) {
    logger.error('复制失败:', err)
    toast.error('复制失败')
  }
}

// 打开日志详情
const openLogDetail = (record: any) => {
  selectedTaskForDetail.value = record as Task
  logDetailOpen.value = true
}

// 事件处理方法
const handleSearch = (formData: Record<string, any>) => {
  searchForm.value = {
    keyword: formData.keyword || '',
    taskStatus: formData.taskStatus || 'all',
  }
  currentPage.value = 1 // 重置到第一页
}

const handleReset = () => {
  searchForm.value = {
    keyword: '',
    taskStatus: 'all',
  }
  currentPage.value = 1
}

const handlePageChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
}

const handleRefreshIntervalChange = () => {
  taskStore.setRefreshInterval(refreshInterval.value)
  setupAutoRefresh()
}

const handleRefresh = async () => {
  loading.value = true
  try {
    await taskStore.updateTaskList()
    // toast.success('任务列表已刷新')
  } catch (error) {
    logger.error('刷新任务列表失败:', error)
    toast.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 统一的操作处理方法
const handleAction = async (action: string, task?: Task) => {
  const actionMap: Record<string, string> = {
    stop: '停止',
    pause: '暂停',
    resume: '恢复',
  }

  const actionName = actionMap[action] || action

  try {
    if (task) {
      toast.info(`正在${actionName}任务: ${task.taskId}`)

      // 调用相应的API
      let response
      switch (action) {
        case 'stop':
          response = await taskService.stopTask(task.taskId)
          break
        case 'pause':
          response = await taskService.pauseTask(task.taskId)
          break
        case 'resume':
          response = await taskService.resumeTask(task.taskId)
          break
        default:
          throw new Error(`未知操作: ${action}`)
      }

      if (response.status === 'Success') {
        toast.success(`${actionName}任务成功: ${task.taskId}`)
        // 刷新任务列表
        await taskStore.updateTaskList()
      } else {
        throw new Error(response.message || `${actionName}任务失败`)
      }
    }
  } catch (error) {
    logger.error(`${actionName}任务失败:`, error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    toast.error(`${actionName}任务失败: ${errorMessage}`)
  }
}

// 单个任务操作点击处理
const handleTaskActionClick = async (action: string, record: any) => {
  const task = record as Task
  if (action === 'delete') {
    openDeleteDialog(task)
  } else {
    await handleAction(action, task)
  }
}

// 删除相关方法
const openDeleteDialog = (task: Task) => {
  taskToDelete.value = task
  isBatchDelete.value = false
  deleteDialogOpen.value = true
}

const openBatchDeleteDialog = () => {
  taskToDelete.value = null
  isBatchDelete.value = true
  deleteDialogOpen.value = true
}

const closeDeleteDialog = () => {
  deleteDialogOpen.value = false
  taskToDelete.value = null
  isBatchDelete.value = false
  deleteLoading.value = false
}

const confirmDeleteTask = async () => {
  deleteLoading.value = true

  try {
    if (isBatchDelete.value) {
      // 批量删除
      const selectedCount = rowSelection.selectedRowKeys.length
      if (selectedCount === 0) {
        toast.warning('请先选择要删除的任务')
        return
      }

      toast.info(`正在批量删除 ${selectedCount} 个任务...`)

      // 批量删除任务
      const deletePromises = rowSelection.selectedRowKeys.map(key => {
        const task = taskStore.tasks.find(t => t.taskId === key)
        return task ? taskService.deleteTask(task.taskId) : Promise.resolve({ status: 'Success' })
      })

      await Promise.all(deletePromises)

      // 清空选中状态
      rowSelection.selectedRowKeys = []

      // 立即刷新任务列表，确保获取最新数据
      await taskStore.updateTaskList()

      // 如果当前页没有数据了，回到上一页
      if (paginatedData.value.length === 0 && currentPage.value > 1) {
        currentPage.value = currentPage.value - 1
      }

      toast.success(`批量删除 ${selectedCount} 个任务成功`)
    } else {
      // 单个删除
      if (!taskToDelete.value) return

      const taskIdToDelete = taskToDelete.value.taskId

      // 调用删除任务 API
      const response = await taskService.deleteTask(taskIdToDelete)

      if (response.status === 'Success') {
        toast.success(`删除任务成功: ${taskIdToDelete}`)

        // 立即刷新任务列表，确保获取最新数据
        await taskStore.updateTaskList()

        // 如果当前页没有数据了，回到上一页
        if (paginatedData.value.length === 0 && currentPage.value > 1) {
          currentPage.value = currentPage.value - 1
        }
      } else {
        throw new Error(response.message || '删除任务失败')
      }
    }

    // 关闭弹框
    closeDeleteDialog()
  } catch (error) {
    logger.error('删除任务失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    toast.error(`删除任务失败: ${errorMessage}`)
  } finally {
    deleteLoading.value = false
  }
}

// 批量操作处理方法
const handleBatchAction = async (action: string) => {
  if (rowSelection.selectedRowKeys.length === 0) {
    toast.warning('请先选择要操作的任务')
    return
  }

  const actionMap: Record<string, string> = {
    stop: '停止',
    pause: '暂停',
    resume: '恢复',
    delete: '删除',
  }

  const actionName = actionMap[action] || action
  const selectedCount = rowSelection.selectedRowKeys.length

  // 删除操作使用弹框确认
  if (action === 'delete') {
    openBatchDeleteDialog()
    return
  }

  try {
    toast.info(`正在批量${actionName} ${selectedCount} 个任务...`)

    // 批量操作任务
    const actionPromises = rowSelection.selectedRowKeys.map(key => {
      const task = taskStore.tasks.find(t => t.taskId === key)
      if (!task) return Promise.resolve({ status: 'Success' })

      switch (action) {
        case 'stop':
          return taskService.stopTask(task.taskId)
        case 'pause':
          return taskService.pauseTask(task.taskId)
        case 'resume':
          return taskService.resumeTask(task.taskId)
        default:
          return Promise.resolve({ status: 'Success' })
      }
    })

    await Promise.all(actionPromises)

    // 刷新任务列表
    await taskStore.updateTaskList()

    // 清空选中状态
    rowSelection.selectedRowKeys = []

    toast.success(`批量${actionName}任务成功`)
  } catch (error) {
    logger.error(`批量${actionName}任务失败:`, error)
    toast.error(`批量${actionName}任务失败`)
  }
}

// 选择变化处理方法
const handleSelectionChange = (keys: (string | number)[], rows: Task[]) => {
  logger.info('表格选择变化', { count: keys.length, rowCount: rows.length, keys })
  rowSelection.selectedRowKeys = keys
}

// 排序变化处理方法
const handleSortChange = (key: string, order: 'asc' | 'desc' | null) => {
  logger.info('表格排序变化', { field: key, order })

  // 更新排序状态
  currentSortField.value = order ? key : null
  currentSortOrder.value = order

  // 重置到第一页，因为排序可能改变数据顺序
  currentPage.value = 1
}

// 自动刷新设置
const setupAutoRefresh = () => {
  // 清除现有定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }

  // 设置新的定时器
  if (refreshInterval.value > 0) {
    refreshTimer.value = setInterval(() => {
      handleRefresh()
    }, refreshInterval.value * 1000)
  }
}

// 监听刷新间隔变化
watch(
  () => refreshInterval.value,
  () => {
    taskStore.setRefreshInterval(refreshInterval.value)
    setupAutoRefresh()
  }
)

// 生命周期钩子
onMounted(async () => {
  // 初始化加载任务列表
  await handleRefresh()

  // 设置自动刷新
  setupAutoRefresh()
})

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
})
</script>

<style lang="scss" scoped>
.page-layout {
  @apply flex flex-col h-full gap-4 p-4;
}

.search-section {
  @apply flex-shrink-0;
}

.table-section {
  @apply flex-1 min-h-0;
}

.pagination-section {
  @apply flex-shrink-0;
}
</style>
