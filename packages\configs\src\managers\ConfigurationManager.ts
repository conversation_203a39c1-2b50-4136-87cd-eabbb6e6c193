import type { Options as TsupOptions } from 'tsup'
import { createTsupConfig, type TsupConfigOptions } from './tsup.config.js'
import { eslintConfig } from '../eslint/index.js'

/**
 * 包类型枚举
 */
export enum PackageType {
  /** 共享工具包 */
  SHARED = 'shared',
  /** UI 组件包 */
  UI = 'ui',
  /** 工作流包 */
  FLOW = 'flow',
  /** Electron 核心包 */
  ELECTRON_CORE = 'electron-core',
  /** 配置包 */
  CONFIGS = 'configs',
  /** Electron 应用 */
  ELECTRON_APP = 'electron-app',
  /** Vue 应用 */
  VUE_APP = 'vue-app'
}

/**
 * TypeScript 配置选项
 */
export interface TsConfigOptions {
  /** 基础配置路径 */
  extends?: string
  /** 编译选项 */
  compilerOptions?: Record<string, any>
  /** 包含文件 */
  include?: string[]
  /** 排除文件 */
  exclude?: string[]
  /** 项目引用 */
  references?: Array<{ path: string }>
}

/**
 * ESLint 配置选项
 */
export interface EslintConfigOptions {
  /** 文件匹配模式 */
  files?: string[]
  /** 忽略模式 */
  ignores?: string[]
  /** 规则配置 */
  rules?: Record<string, any>
  /** 扩展配置 */
  extends?: string[]
}

/**
 * 包配置信息
 */
export interface PackageInfo {
  /** 包名 */
  name: string
  /** 包类型 */
  type: PackageType
  /** 包路径 */
  path: string
  /** 是否为应用 */
  isApp: boolean
  /** 是否包含 Vue */
  hasVue: boolean
  /** 是否包含 Electron */
  hasElectron: boolean
}

/**
 * 配置管理器类
 * 统一管理 Monorepo 中所有包的配置文件生成和管理
 */
export class ConfigurationManager {
  private static instance: ConfigurationManager

  private constructor() {}

  /**
   * 获取配置管理器单例实例
   */
  public static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager()
    }
    return ConfigurationManager.instance
  }

  /**
   * 识别包类型
   * @param packageName 包名
   * @param packagePath 包路径
   * @returns 包类型
   */
  public identifyPackageType(packageName: string, packagePath: string): PackageType {
    // 应用类型判断
    if (packagePath.startsWith('apps/')) {
      return PackageType.ELECTRON_APP
    }

    // 包类型判断
    if (packageName.includes('shared')) {
      return PackageType.SHARED
    }
    if (packageName.includes('ui') || packageName.includes('components')) {
      return PackageType.UI
    }
    if (packageName.includes('flow') || packageName.includes('workflow')) {
      return PackageType.FLOW
    }
    if (packageName.includes('electron-core')) {
      return PackageType.ELECTRON_CORE
    }
    if (packageName.includes('configs')) {
      return PackageType.CONFIGS
    }

    // 默认为共享包
    return PackageType.SHARED
  }

  /**
   * 生成 TypeScript 配置
   * @param packageType 包类型
   * @param customOptions 自定义选项
   * @returns TypeScript 配置对象
   */
  public generateTsConfig(
    packageType: PackageType,
    customOptions: Partial<TsConfigOptions> = {}
  ): TsConfigOptions {
    switch (packageType) {
      case PackageType.ELECTRON_APP:
        return {
          extends: '../../packages/configs/src/typescript/electron-app.json',
          compilerOptions: {
            baseUrl: '.',
            paths: {
              '@/*': ['./src/renderer/src/*'],
              '@main/*': ['./src/main/*'],
              '@preload/*': ['./src/preload/*'],
              '@mattverse/shared': ['../../packages/shared/src'],
              '@mattverse/mattverse-ui': ['../../packages/mattverse-ui/src'],
              '@mattverse/mattverse-flow': ['../../packages/mattverse-flow/src'],
              '@mattverse/electron-core': ['../../packages/electron-core/src'],
              '@mattverse/configs': ['../../packages/configs/src'],
            },
          },
          include: ['src/**/*', 'electron.vite.config.ts'],
          exclude: ['node_modules', 'dist', 'out', 'release'],
          ...customOptions,
        }

      case PackageType.UI:
        return {
          extends: '../configs/src/typescript/ui-package.json',
          compilerOptions: {
            baseUrl: '.',
            outDir: './dist',
            rootDir: './src',
            paths: {
              '@/*': ['./src/*'],
            },
          },
          include: ['src/**/*.ts', 'src/**/*.vue', 'vite.config.ts'],
          exclude: ['node_modules', 'dist', '**/*.test.*', '**/*.spec.*'],
          ...customOptions,
        }

      case PackageType.FLOW:
        return {
          extends: '../configs/src/typescript/vue.json',
          compilerOptions: {
            baseUrl: '.',
            outDir: './dist',
            rootDir: './src',
            paths: {
              '@/*': ['./src/*'],
            },
          },
          include: ['src/**/*.ts', 'src/**/*.vue'],
          exclude: ['node_modules', 'dist', '**/*.test.*', '**/*.spec.*'],
          ...customOptions,
        }

      case PackageType.CONFIGS:
        return {
          extends: './src/typescript/base.json',
          compilerOptions: {
            outDir: './dist',
            rootDir: './src',
            baseUrl: '.',
            paths: {
              '@/*': ['./src/*'],
            },
          },
          include: ['src/**/*'],
          exclude: ['node_modules', 'dist', '**/*.test.*', '**/*.spec.*'],
          ...customOptions,
        }

      case PackageType.ELECTRON_CORE:
        return {
          extends: '../configs/src/typescript/package.json',
          compilerOptions: {
            baseUrl: '.',
            outDir: './dist',
            rootDir: './src',
            target: 'ES2022',
            types: ['node', 'electron'],
            paths: {
              '@/*': ['./src/*'],
            },
          },
          include: ['src/**/*'],
          exclude: ['node_modules', 'dist', '**/*.test.*', '**/*.spec.*'],
          ...customOptions,
        }

      default:
        return {
          extends: '../configs/src/typescript/package.json',
          compilerOptions: {
            baseUrl: '.',
            outDir: './dist',
            rootDir: './src',
            paths: {
              '@/*': ['./src/*'],
            },
          },
          include: ['src/**/*'],
          exclude: ['node_modules', 'dist', '**/*.test.*', '**/*.spec.*'],
          ...customOptions,
        }
    }
  }

  /**
   * 生成 Tsup 构建配置
   * @param packageName 包名
   * @param customOptions 自定义选项
   * @returns Tsup 配置对象
   */
  public generateTsupConfig(
    packageName: string,
    customOptions: TsupConfigOptions = {}
  ): TsupOptions {
    const packageType = this.identifyPackageType(packageName, '')

    switch (packageType) {
      case PackageType.UI:
        return createTsupConfig({
          target: 'es2020',
          external: ['vue', '@vue/*'],
          ...customOptions,
        })

      case PackageType.FLOW:
        return createTsupConfig({
          external: ['vue', '@vue-flow/*'],
          ...customOptions,
        })

      case PackageType.ELECTRON_CORE:
        return createTsupConfig({
          external: ['electron'],
          platform: 'node',
          format: ['esm', 'cjs'],
          ...customOptions,
        })

      case PackageType.CONFIGS:
        return createTsupConfig({
          external: [
            'vue', '@vue/*', 'electron', 'vite', 'tailwindcss',
            'eslint', 'typescript', 'tsup', 'path', 'fs', 'url'
          ],
          platform: 'node',
          ...customOptions,
        })

      default:
        return createTsupConfig(customOptions)
    }
  }

  /**
   * 生成 ESLint 配置
   * @param packageType 包类型
   * @param customOptions 自定义选项
   * @returns ESLint 配置对象
   */
  public generateEslintConfig(
    packageType: PackageType,
    customOptions: EslintConfigOptions = {}
  ): any[] {
    const baseConfig = [...eslintConfig]

    // 根据包类型添加特定配置
    if (packageType === PackageType.UI || packageType === PackageType.FLOW) {
      baseConfig.push({
        files: ['**/*.vue'],
        rules: {
          'vue/multi-word-component-names': 'off',
          'vue/no-v-html': 'warn',
        },
      })
    }

    if (customOptions.rules) {
      baseConfig.push({
        files: customOptions.files || ['**/*.{js,ts,vue}'],
        rules: customOptions.rules,
      })
    }

    return baseConfig
  }

  /**
   * 生成包的标准 package.json 脚本
   * @param packageType 包类型
   * @returns 标准脚本对象
   */
  public generatePackageScripts(packageType: PackageType): Record<string, string> {
    const baseScripts = {
      clean: 'rimraf dist',
      lint: 'eslint src --ext .ts,.tsx',
      'lint:fix': 'eslint src --ext .ts,.tsx --fix',
      typecheck: 'tsc --noEmit',
    }

    switch (packageType) {
      case PackageType.ELECTRON_APP:
        return {
          dev: 'electron-vite dev',
          build: 'electron-vite build',
          preview: 'electron-vite preview',
          'build:win': 'pnpm run build && electron-builder --win',
          'build:mac': 'pnpm run build && electron-builder --mac',
          'build:linux': 'pnpm run build && electron-builder --linux',
          clean: 'rimraf dist release',
          lint: 'eslint src --ext .ts,.tsx,.vue',
          'lint:fix': 'eslint src --ext .ts,.tsx,.vue --fix',
          typecheck: 'vue-tsc --noEmit',
        }

      case PackageType.UI:
        return {
          build: 'vite build && vue-tsc --project tsconfig.build.json',
          dev: 'vite build --watch',
          ...baseScripts,
          lint: 'eslint src --ext .ts,.tsx,.vue',
          'lint:fix': 'eslint src --ext .ts,.tsx,.vue --fix',
          typecheck: 'vue-tsc --noEmit',
        }

      case PackageType.FLOW:
        return {
          build: 'tsup',
          dev: 'tsup --watch',
          ...baseScripts,
          lint: 'eslint src --ext .ts,.tsx,.vue',
          'lint:fix': 'eslint src --ext .ts,.tsx,.vue --fix',
          typecheck: 'vue-tsc --noEmit',
        }

      default:
        return {
          build: 'tsup',
          dev: 'tsup --watch',
          ...baseScripts,
        }
    }
  }

  /**
   * 验证配置的有效性
   * @param config 配置对象
   * @param configType 配置类型
   * @returns 验证结果
   */
  public validateConfig(config: any, configType: string): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    try {
      switch (configType) {
        case 'tsconfig':
          if (!config.compilerOptions) {
            errors.push('缺少 compilerOptions 配置')
          }
          break
        case 'tsup':
          if (!config.entry) {
            errors.push('缺少 entry 配置')
          }
          break
        case 'eslint':
          if (!Array.isArray(config)) {
            errors.push('ESLint 配置必须是数组格式')
          }
          break
      }
    } catch (error) {
      errors.push(`配置验证失败: ${error}`)
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }
}
