<template>
  <div
    v-if="isWorkflowPage && navbarStore.workflowTabs.length > 0"
    class="flex items-center gap-1 py-1"
  >
    <!-- 分隔符 -->
    <Separator orientation="vertical" class="h-4 mx-2" />

    <!-- 工作流标签页 -->
    <div class="flex items-center gap-1 max-w-4xl overflow-hidden">
      <!-- 标签页容器 -->
      <div class="flex items-center gap-1 overflow-x-auto scrollbar-none">
        <template v-for="tab in visibleTabs" :key="tab.id">
          <ContextMenu>
            <ContextMenuTrigger as-child>
              <div
                class="flex items-center gap-1 px-2 py-0.5 rounded-md border transition-all duration-200 cursor-pointer group min-w-0"
                :class="[
                  tab.isActive
                    ? 'bg-primary text-primary-foreground border-primary shadow-sm'
                    : 'bg-muted/50 text-muted-foreground border-border hover:bg-muted hover:text-foreground',
                ]"
                @click="handleTabClick(tab)"
              >
                <!-- 工作流图标 -->
                <MattIcon
                  name="Workflow"
                  :class="[
                    'h-3 w-3 flex-shrink-0',
                    tab.isActive ? 'text-primary-foreground' : 'text-muted-foreground',
                  ]"
                />

                <!-- 工作流信息 -->
                <div class="flex flex-col min-w-0 max-w-[240px] workflow-info">
                  <span class="text-xs font-semibold truncate" :title="tab.name">
                    {{ tab.name }}
                  </span>
                  <span class="text-[10px] opacity-75 truncate" :title="`ID: ${tab.id}`">
                    ID: {{ tab.id }}
                  </span>
                </div>

                <!-- 未保存标识 -->
                <div
                  v-if="tab.isDirty"
                  class="w-1.5 h-1.5 rounded-full bg-orange-500 flex-shrink-0"
                  title="有未保存的更改"
                />

                <!-- 关闭按钮 -->
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0 hover:bg-destructive hover:text-destructive-foreground"
                  @click.stop="handleCloseTab(tab.id)"
                >
                  <MattIcon name="X" class="h-2.5 w-2.5" />
                </Button>
              </div>
            </ContextMenuTrigger>

            <!-- 右键菜单内容 -->
            <ContextMenuContent class="w-48 border-0">
              <ContextMenuItem @click="handleCloseTab(tab.id)">
                <MattIcon name="X" class="mr-2 h-4 w-4" />
                关闭当前
              </ContextMenuItem>

              <ContextMenuItem @click="handleCloseOthers(tab.id)">
                <MattIcon name="XCircle" class="mr-2 h-4 w-4" />
                关闭其它
              </ContextMenuItem>

              <ContextMenuSeparator />

              <ContextMenuItem @click="handleCloseLeft(tab.id)">
                <MattIcon name="ChevronLeft" class="mr-2 h-4 w-4" />
                关闭左侧标签页
              </ContextMenuItem>

              <ContextMenuItem @click="handleCloseRight(tab.id)">
                <MattIcon name="ChevronRight" class="mr-2 h-4 w-4" />
                关闭右侧标签页
              </ContextMenuItem>

              <ContextMenuSeparator />

              <ContextMenuItem @click="handleCloseAll">
                <MattIcon name="Trash2" class="mr-2 h-4 w-4" />
                全部关闭
              </ContextMenuItem>
            </ContextMenuContent>
          </ContextMenu>
        </template>
      </div>

      <!-- 更多标签页指示器 -->
      <DropdownMenu v-if="hasHiddenTabs">
        <DropdownMenuTrigger as-child>
          <Button
            variant="ghost"
            size="sm"
            class="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
          >
            <MattIcon name="MoreHorizontal" class="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" class="w-[600px] max-w-[85vw] p-2 border-0">
          <DropdownMenuLabel class="mb-2">更多工作流</DropdownMenuLabel>
          <DropdownMenuSeparator class="mb-3" />

          <!-- 网格布局的工作流卡片 -->
          <div
            class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 max-h-[200px] overflow-y-auto scrollbar"
          >
            <div
              v-for="tab in hiddenTabs"
              :key="tab.id"
              class="group cursor-pointer transition-all duration-200 hover:shadow-md flex flex-col border rounded-lg p-2 min-h-[80px]"
              :class="[
                tab.isActive
                  ? 'bg-primary text-primary-foreground border-primary shadow-sm'
                  : 'bg-card text-card-foreground border-border hover:bg-accent hover:text-accent-foreground',
              ]"
              @click="handleTabClick(tab)"
            >
              <!-- 头部：图标和关闭按钮 -->
              <div class="flex items-start justify-between mb-1">
                <div class="p-1 rounded bg-muted/50">
                  <MattIcon
                    name="Workflow"
                    :class="[
                      'h-4 w-4',
                      tab.isActive ? 'text-primary-foreground' : 'text-muted-foreground',
                    ]"
                  />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-destructive hover:text-destructive-foreground"
                  @click.stop="handleCloseTab(tab.id)"
                >
                  <MattIcon name="X" class="h-3 w-3" />
                </Button>
              </div>

              <!-- 工作流信息 -->
              <div class="flex-1 flex flex-col justify-between">
                <div>
                  <h3 class="text-xs font-semibold truncate mb-0.5" :title="tab.name">
                    {{ tab.name }}
                  </h3>
                  <p class="text-[10px] opacity-75 truncate" :title="`ID: ${tab.id}`">
                    ID: {{ tab.id }}
                  </p>
                </div>

                <!-- 底部：状态指示器 -->
                <div class="flex items-center justify-between mt-1">
                  <div class="flex items-center gap-1">
                    <div
                      v-if="tab.isDirty"
                      class="w-2 h-2 rounded-full bg-orange-500"
                      title="有未保存的更改"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { useNavbarStore, type WorkflowTab } from '@/store/modules/nav/navbar'

const router = useRouter()
const route = useRoute()
const navbarStore = useNavbarStore()

// 判断是否为工作流相关页面
const isWorkflowPage = computed(() => {
  return route.path.startsWith('/workflow')
})

// 最大显示标签页数量
const MAX_VISIBLE_TABS = 5

// 可见的标签页（确保活跃标签页始终可见）
const visibleTabs = computed(() => {
  const allTabs = navbarStore.workflowTabs
  if (allTabs.length <= MAX_VISIBLE_TABS) {
    return allTabs
  }

  // 找到活跃标签页的索引
  const activeIndex = allTabs.findIndex((tab) => tab.isActive)

  if (activeIndex === -1 || activeIndex < MAX_VISIBLE_TABS) {
    // 活跃标签页在前5个或没有活跃标签页，直接返回前5个
    return allTabs.slice(0, MAX_VISIBLE_TABS)
  } else {
    // 活跃标签页在后面，需要调整显示的标签页
    // 将活跃标签页放在最后一个位置，前面显示其他4个
    const visibleTabsArray = allTabs.slice(0, MAX_VISIBLE_TABS - 1)
    visibleTabsArray.push(allTabs[activeIndex])
    return visibleTabsArray
  }
})

// 隐藏的标签页（不在可见列表中的标签页）
const hiddenTabs = computed(() => {
  const visibleTabIds = new Set(visibleTabs.value.map((tab: any) => tab.id))
  return navbarStore.workflowTabs.filter((tab: any) => !visibleTabIds.has(tab.id))
})

// 是否有隐藏的标签页
const hasHiddenTabs = computed(() => {
  return navbarStore.workflowTabs.length > MAX_VISIBLE_TABS
})

// 处理标签页点击
const handleTabClick = (tab: WorkflowTab) => {
  if (!tab.isActive) {
    navbarStore.setActiveWorkflowTab(tab.id)
    router.push(tab.path)
  }
}

// 处理关闭标签页
const handleCloseTab = (tabId: string) => {
  const tab = navbarStore.workflowTabs.find((t: any) => t.id === tabId)

  if (tab?.isDirty) {
    // 如果有未保存的更改，可以在这里添加确认对话框
    // 暂时直接关闭
  }

  navbarStore.removeWorkflowTab(tabId)

  // 如果关闭的是当前活跃标签，且还有其他标签，跳转到新的活跃标签
  if (tab?.isActive && navbarStore.workflowTabs.length > 0) {
    const activeTab = navbarStore.activeWorkflowTab
    if (activeTab) {
      router.push(activeTab.path)
    }
  } else if (navbarStore.workflowTabs.length === 0) {
    // 如果没有标签页了，跳转到工作流列表
    router.push('/workflow')
  }
}

// 右键菜单处理函数
// 关闭其它标签页
const handleCloseOthers = (currentTabId: string) => {
  const currentTab = navbarStore.workflowTabs.find((t: any) => t.id === currentTabId)
  if (!currentTab) return

  // 获取要关闭的标签页列表（除了当前标签页）
  const tabsToClose = navbarStore.workflowTabs.filter((t: any) => t.id !== currentTabId)

  // 逐个关闭其他标签页
  tabsToClose.forEach((tab: any) => {
    navbarStore.removeWorkflowTab(tab.id)
  })

  // 确保当前标签页是活跃的
  if (!currentTab.isActive) {
    navbarStore.setActiveWorkflowTab(currentTabId)
    router.push(currentTab.path)
  }
}

// 关闭左侧标签页
const handleCloseLeft = (currentTabId: string) => {
  const currentIndex = navbarStore.workflowTabs.findIndex((t: any) => t.id === currentTabId)
  if (currentIndex <= 0) return

  // 获取左侧的标签页
  const leftTabs = navbarStore.workflowTabs.slice(0, currentIndex)

  // 关闭左侧标签页
  leftTabs.forEach((tab: any) => {
    navbarStore.removeWorkflowTab(tab.id)
  })
}

// 关闭右侧标签页
const handleCloseRight = (currentTabId: string) => {
  const currentIndex = navbarStore.workflowTabs.findIndex((t: any) => t.id === currentTabId)
  if (currentIndex === -1 || currentIndex >= navbarStore.workflowTabs.length - 1) return

  // 获取右侧的标签页
  const rightTabs = navbarStore.workflowTabs.slice(currentIndex + 1)

  // 关闭右侧标签页
  rightTabs.forEach((tab: any) => {
    navbarStore.removeWorkflowTab(tab.id)
  })
}

// 全部关闭
const handleCloseAll = () => {
  // 关闭所有标签页
  const allTabs = [...navbarStore.workflowTabs]
  allTabs.forEach((tab: any) => {
    navbarStore.removeWorkflowTab(tab.id)
  })

  // 跳转到工作流列表
  router.push('/workflow')
}
</script>

<style scoped>
/* 隐藏滚动条但保持滚动功能 */
.scrollbar-none {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* 确保标签页在小屏幕上的响应式行为 */
@media (max-width: 768px) {
  .workflow-info {
    max-width: 100px;
  }
}

@media (max-width: 640px) {
  .workflow-info {
    max-width: 80px;
  }
}
</style>
