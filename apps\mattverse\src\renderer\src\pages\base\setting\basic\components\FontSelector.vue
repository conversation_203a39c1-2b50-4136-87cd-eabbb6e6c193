<template>
  <div class="space-y-6">
    <!-- 字体分类选择 -->
    <div class="space-y-3">
      <Label class="text-sm font-medium">{{ $t('settings.font_category') }}</Label>
      <div class="grid grid-cols-3 gap-2">
        <Button
          v-for="category in fontCategories"
          :key="category.key"
          :variant="selectedCategory === category.key ? 'default' : 'outline'"
          size="sm"
          @click="selectedCategory = category.key"
        >
          <MattIcon :name="category.icon" class="mr-2 h-4 w-4" />
          {{ category.label }}
        </Button>
      </div>
    </div>

    <Separator />

    <!-- 字体族设置 -->
    <div class="space-y-3">
      <Label class="text-sm font-medium">{{ $t('settings.font_family') }}</Label>
      <Select :model-value="selectedFontFamily" @update:model-value="updateFontFamily">
        <SelectTrigger class="w-full">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem v-for="font in currentCategoryFonts" :key="font.value" :value="font.value">
            <div class="flex flex-col">
              <span :style="{ fontFamily: font.cssVar }">{{ font.label }}</span>
              <span class="text-xs text-muted-foreground">{{ font.description }}</span>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    </div>

    <Separator />

    <!-- 字号设置 -->
    <div class="space-y-3">
      <div class="flex items-center justify-between">
        <Label class="text-sm font-medium">{{ $t('settings.font_size') }}</Label>
        <span class="text-sm text-muted-foreground">{{ selectedFontSize }}px</span>
      </div>
      <div class="space-y-4">
        <Slider
          :model-value="[selectedFontSize]"
          :max="32"
          :min="10"
          :step="1"
          class="flex-1"
          @update:model-value="updateFontSize($event[0])"
        />
        <div class="grid grid-cols-6 gap-2">
          <Button
            v-for="size in presetSizes"
            :key="size"
            :variant="selectedFontSize === size ? 'default' : 'outline'"
            size="sm"
            @click="updateFontSize(size)"
          >
            {{ size }}px
          </Button>
        </div>
      </div>
    </div>

    <Separator />

    <!-- 预览区域 -->
    <div class="space-y-3">
      <Label class="text-sm font-medium">{{ $t('settings.font_preview') }}</Label>
      <div
        class="rounded-lg border bg-card p-4 text-card-foreground space-y-3"
        :style="{
          fontFamily: getCurrentFontCssVar(),
          fontSize: selectedFontSize + 'px',
        }"
      >
        <div>
          <h3 class="font-semibold mb-2">{{ $t('settings.font_preview_title') }}</h3>
          <p class="mb-2">{{ $t('settings.font_preview_text') }}</p>
          <p class="text-sm text-muted-foreground mb-2">
            The quick brown fox jumps over the lazy dog. 1234567890
          </p>
          <p class="text-xs text-muted-foreground">
            ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789
          </p>
        </div>

        <!-- 不同字号预览 -->
        <div class="space-y-2 pt-3 border-t">
          <div class="text-xs text-muted-foreground">{{ $t('settings.font_size_preview') }}:</div>
          <div class="space-y-1">
            <div :style="{ fontSize: '12px' }">12px - 小号文字</div>
            <div :style="{ fontSize: '14px' }">14px - 正文文字</div>
            <div :style="{ fontSize: '16px' }">16px - 标题文字</div>
            <div :style="{ fontSize: '18px' }">18px - 大标题</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { useSettingsStore } from '@/store'

const { t } = useI18n()
const settingsStore = useSettingsStore()

const selectedCategory = ref<'sans' | 'serif' | 'mono'>('sans')

const selectedFontFamily = computed({
  get: () => settingsStore.font.fontFamily,
  set: (value: string) => settingsStore.updateFont({ fontFamily: value }),
})

const selectedFontSize = computed({
  get: () => settingsStore.font.fontSize,
  set: (value: number) => settingsStore.updateFont({ fontSize: value }),
})

// 字体分类
const fontCategories = computed(() => [
  {
    key: 'sans' as const,
    label: t('settings.font_categories.sans'),
    icon: 'Type',
  },
  {
    key: 'serif' as const,
    label: t('settings.font_categories.serif'),
    icon: 'FileText',
  },
  {
    key: 'mono' as const,
    label: t('settings.font_categories.mono'),
    icon: 'Code',
  },
])

// 所有字体选项 - 基于 theme.css 中的定义
const allFontOptions = computed(() => ({
  sans: [
    {
      value: 'system-ui',
      label: t('settings.font_options.system'),
      description: 'system-ui, -apple-system, sans-serif',
      cssVar: 'system-ui, -apple-system, sans-serif',
    },
    {
      value: '--font-sans',
      label: t('settings.font_options.default'),
      description: 'NotoSansSC-Regular, SourceHanSansSC',
      cssVar: 'var(--font-sans)',
    },
    {
      value: '--font-noto',
      label: t('settings.font_options.noto'),
      description: 'NotoSansSC',
      cssVar: 'var(--font-noto)',
    },
    {
      value: '--font-noto-light',
      label: t('settings.font_options.noto_light'),
      description: 'NotoSansSC Light',
      cssVar: 'var(--font-noto-light)',
    },
    {
      value: '--font-noto-medium',
      label: t('settings.font_options.noto_medium'),
      description: 'NotoSansSC Medium',
      cssVar: 'var(--font-noto-medium)',
    },
    {
      value: '--font-noto-extrabold',
      label: t('settings.font_options.noto_extrabold'),
      description: 'NotoSansSC ExtraBold',
      cssVar: 'var(--font-noto-extrabold)',
    },
    {
      value: '--font-source-han-sc',
      label: t('settings.font_options.source_han_sc'),
      description: 'SourceHanSansSC',
      cssVar: 'var(--font-source-han-sc)',
    },
    {
      value: '--font-alibaba-puhuiti',
      label: t('settings.font_options.alibaba_puhuiti'),
      description: 'AlibabaPuHuiTi',
      cssVar: 'var(--font-alibaba-puhuiti)',
    },
    {
      value: '--font-alibaba-hk',
      label: t('settings.font_options.alibaba_hk'),
      description: 'AlibabaSansHK',
      cssVar: 'var(--font-alibaba-hk)',
    },
  ],
  serif: [
    {
      value: '--font-serif',
      label: t('settings.font_options.serif'),
      description: 'Georgia, Cambria, Times New Roman',
      cssVar: 'var(--font-serif)',
    },
  ],
  mono: [
    {
      value: '--font-mono',
      label: t('settings.font_options.monospace'),
      description: 'SFMono-Regular, Menlo, Monaco',
      cssVar: 'var(--font-mono)',
    },
  ],
}))

// 当前分类的字体选项
const currentCategoryFonts = computed(() => {
  return allFontOptions.value[selectedCategory.value] || []
})

// 预设字号
const presetSizes = [10, 12, 14, 16, 18, 20]

// 获取当前字体的 CSS 变量
const getCurrentFontCssVar = () => {
  const fontMap: Record<string, string> = {
    'system-ui': 'system-ui, -apple-system, sans-serif',
    '--font-sans': 'var(--font-sans)',
    '--font-serif': 'var(--font-serif)',
    '--font-mono': 'var(--font-mono)',
    '--font-noto': "'NotoSansSC', sans-serif",
    '--font-noto-light': "'NotoSansSC', sans-serif",
    '--font-noto-regular': "'NotoSansSC', sans-serif",
    '--font-noto-medium': "'NotoSansSC', sans-serif",
    '--font-noto-extrabold': "'NotoSansSC', sans-serif",
    '--font-noto-black': "'NotoSansSC', sans-serif",
    '--font-alibaba-puhuiti': "'AlibabaPuHuiTi', sans-serif",
    '--font-alibaba-hk': "'AlibabaSansHK', sans-serif",
    '--font-source-han-sc': "'SourceHanSansSC', sans-serif",
    '--font-source-han-tc': "'SourceHanSansTC', sans-serif",
    '--font-source-han-hc': "'SourceHanSansHC', sans-serif",
    '--font-source-han-hk': "'SourceHanSansHK', sans-serif",
    '--font-source-han-k': "'SourceHanSansK', sans-serif",
  }

  return fontMap[selectedFontFamily.value] || 'var(--font-sans)'
}

// 监听分类变化，自动选择该分类的第一个字体
watch(selectedCategory, (newCategory) => {
  const categoryFonts = allFontOptions.value[newCategory]
  if (categoryFonts && categoryFonts.length > 0) {
    // 检查当前字体是否在新分类中
    const currentFontInCategory = categoryFonts.find(
      (font) => font.value === selectedFontFamily.value
    )
    if (!currentFontInCategory) {
      selectedFontFamily.value = categoryFonts[0].value
    }
  }
})

// 根据当前字体设置初始分类
const initializeCategory = () => {
  const currentFont = selectedFontFamily.value
  for (const [category, fonts] of Object.entries(allFontOptions.value)) {
    if (fonts.some((font) => font.value === currentFont)) {
      selectedCategory.value = category as 'sans' | 'serif' | 'mono'
      break
    }
  }
}

// 初始化
initializeCategory()

const updateFontFamily = (value: string) => {
  selectedFontFamily.value = value
}

const updateFontSize = (value: number) => {
  selectedFontSize.value = value
}
</script>
