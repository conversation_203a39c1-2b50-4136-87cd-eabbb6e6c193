// 组件相关翻译
export default {
  // 侧边栏组件
  sidebar: {
    toggle: '切换侧边栏',
    collapse: '收起侧边栏',
    expand: '展开侧边栏',
  },
  
  // 语言选择器
  language_selector: {
    title: '选择语言',
    current: '当前语言',
  },
  
  // 主题选择器
  theme_selector: {
    title: '选择主题',
    light: '浅色主题',
    dark: '深色主题',
    system: '跟随系统',
  },
  
  // 表格组件
  table: {
    no_data: '暂无数据',
    loading: '数据加载中...',
    total_items: '共 {count} 项',
    items_per_page: '每页显示',
    page_of: '第 {current} 页，共 {total} 页',
    first_page: '首页',
    last_page: '末页',
    previous_page: '上一页',
    next_page: '下一页',
  },
  
  // 表单组件
  form: {
    required: '此字段为必填项',
    invalid_email: '请输入有效的邮箱地址',
    invalid_url: '请输入有效的URL',
    min_length: '最少需要 {min} 个字符',
    max_length: '最多允许 {max} 个字符',
    password_mismatch: '两次输入的密码不一致',
  },
  
  // 对话框组件
  dialog: {
    confirm_title: '确认操作',
    confirm_message: '您确定要执行此操作吗？',
    delete_title: '确认删除',
    delete_message: '删除后无法恢复，您确定要删除吗？',
  },
  
  // 文件上传组件
  file_upload: {
    drag_drop: '拖拽文件到此处，或点击选择文件',
    select_file: '选择文件',
    file_too_large: '文件大小超过限制',
    invalid_file_type: '不支持的文件类型',
    upload_success: '文件上传成功',
    upload_failed: '文件上传失败',
  },
  
  // 搜索组件
  search: {
    placeholder: '请输入搜索关键词',
    no_results: '未找到相关结果',
    results_count: '找到 {count} 个结果',
  },
}
