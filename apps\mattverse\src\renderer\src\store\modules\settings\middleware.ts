import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { createPersistConfig } from '@/store/plugins/persist-config'
import { logger } from '@mattverse/shared'

/**
 * 连接状态类型
 */
export type ConnectionState = 'IDLE' | 'CONNECTING' | 'READY' | 'TRANSIENT_FAILURE' | 'SHUTDOWN'

/**
 * 中台配置接口
 */
export interface MiddlewareConfig {
  host: string
  port: number
  timeout: number
  autoReconnect: boolean
}

/**
 * 中台状态接口
 */
export interface MiddlewareState {
  config: MiddlewareConfig
  originalConfig: MiddlewareConfig
  connectionStatus: ConnectionState
  middlewareVersion: string
  isConnecting: boolean
  lastConnectedAt?: string
  lastError?: string
}

/**
 * 默认中台设置
 */
const defaultMiddlewareSettings: MiddlewareState = {
  config: {
    host: '***********',
    port: 29998,
    timeout: 30,
    autoReconnect: true,
  },
  originalConfig: {
    host: '***********',
    port: 29998,
    timeout: 30,
    autoReconnect: true,
  },
  connectionStatus: 'IDLE',
  middlewareVersion: '',
  isConnecting: false,
}

/**
 * 中台设置状态管理
 */
export const useMiddlewareStore = defineStore(
  'middleware',
  () => {
    // 状态
    const config = ref<MiddlewareConfig>({ ...defaultMiddlewareSettings.config })
    const originalConfig = ref<MiddlewareConfig>({ ...defaultMiddlewareSettings.originalConfig })
    const connectionStatus = ref<ConnectionState>(defaultMiddlewareSettings.connectionStatus)
    const middlewareVersion = ref<string>(defaultMiddlewareSettings.middlewareVersion)
    const isConnecting = ref<boolean>(defaultMiddlewareSettings.isConnecting)
    const lastConnectedAt = ref<string | undefined>(defaultMiddlewareSettings.lastConnectedAt)
    const lastError = ref<string | undefined>(defaultMiddlewareSettings.lastError)

    // 计算属性
    const hasChanges = computed(() => {
      return (
        config.value.host !== originalConfig.value.host ||
        config.value.port !== originalConfig.value.port ||
        config.value.timeout !== originalConfig.value.timeout ||
        config.value.autoReconnect !== originalConfig.value.autoReconnect
      )
    })

    const currentAddress = computed(() => {
      if (originalConfig.value.host && originalConfig.value.port) {
        return `${originalConfig.value.host}:${originalConfig.value.port}`
      }
      return '--'
    })

    const isConnected = computed(() => connectionStatus.value === 'READY')

    // 方法
    const updateConfig = (updates: Partial<MiddlewareConfig>) => {
      // 只更新实际发生变化的字段
      Object.keys(updates).forEach(key => {
        const typedKey = key as keyof MiddlewareConfig
        if (config.value[typedKey] !== updates[typedKey]) {
          ;(config.value as any)[typedKey] = updates[typedKey]
        }
      })
    }

    const setConnectionStatus = (status: ConnectionState) => {
      connectionStatus.value = status
      if (status === 'READY') {
        lastConnectedAt.value = new Date().toISOString()
        lastError.value = undefined
      } else if (status === 'TRANSIENT_FAILURE') {
        lastError.value = 'Connection failed'
      }
    }

    const setMiddlewareVersion = (version: string) => {
      middlewareVersion.value = version
    }

    const setConnecting = (connecting: boolean) => {
      isConnecting.value = connecting
    }

    // 加载配置
    const loadConfig = async () => {
      try {
        const result = await window.electronAPI.middleware.getConfig()

        if (result.success) {
          config.value.host = result.data.host
          config.value.port = result.data.port
          // 如果后端返回了 timeout 和 autoReconnect，则使用它们
          if (result.data.timeout !== undefined) {
            config.value.timeout = result.data.timeout
          }
          if (result.data.autoReconnect !== undefined) {
            config.value.autoReconnect = result.data.autoReconnect
          }
          originalConfig.value = { ...config.value }
          logger.info('Loaded middleware config:', result.data)
        } else {
          logger.error('Failed to load middleware config:', result.error)
        }
      } catch (error) {
        logger.error('Error loading middleware config:', error)
      }
    }

    // 测试连接
    const testConnection = async () => {
      if (!config.value.host || !config.value.port) {
        return false
      }

      setConnecting(true)
      setConnectionStatus('CONNECTING')

      try {
        const result = await window.electronAPI.middleware.testConnection(
          config.value.host,
          config.value.port
        )

        if (result.success && result.connected) {
          setConnectionStatus('READY')
          // 获取版本信息
          await getVersion()
          setConnecting(false)
          return true
        } else {
          setConnectionStatus('TRANSIENT_FAILURE')
          lastError.value = result.error || 'Connection test failed'
          logger.error('Connection test failed:', result.error)
        }
      } catch (error) {
        setConnectionStatus('TRANSIENT_FAILURE')
        lastError.value = error instanceof Error ? error.message : 'Connection test failed'
        logger.error('Connection test error:', error)
      }

      setConnecting(false)
      return false
    }

    // 获取版本信息
    const getVersion = async () => {
      try {
        const result = await window.electronAPI.middleware.getVersion()

        if (result.success && result.data) {
          // 从服务器响应中提取版本信息
          const version = result.data.result || result.data.version || result.data.api_version || 'Unknown'
          setMiddlewareVersion(version)
          logger.info('Got middleware version:', { version })
        } else {
          setMiddlewareVersion('Unknown')
          logger.error('Failed to get version:', result.error)
        }
      } catch (error) {
        setMiddlewareVersion('Unknown')
        logger.error('Error getting version:', error)
      }
    }

    // 保存配置
    const saveConfig = async () => {
      try {
        const result = await window.electronAPI.middleware.updateConfig(
          config.value.host,
          config.value.port
        )

        if (result.success) {
          // 更新原始配置，包括timeout和autoReconnect
          originalConfig.value = { ...config.value }
          logger.info('Middleware configuration saved successfully')
          return true
        } else {
          logger.error('Failed to save configuration:', result.error)
          throw new Error(result.error)
        }
      } catch (error) {
        logger.error('Error saving configuration:', error)
        throw error
      }
    }

    // 重启应用
    const restartApp = async () => {
      try {
        const result = await window.electronAPI.middleware.restartApp()
        return result
      } catch (error) {
        logger.error('Error restarting app:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Restart failed',
        }
      }
    }

    // 检查gRPC连接状态
    const checkGrpcStatus = async () => {
      try {
        const result = await window.electronAPI.grpc.getStatus()

        if (result.connected) {
          setConnectionStatus('READY')
        } else {
          setConnectionStatus('IDLE')
        }

        return result.connected
      } catch (error) {
        logger.error('Error checking gRPC status:', error)
        setConnectionStatus('TRANSIENT_FAILURE')
        return false
      }
    }

    // 重置配置
    const resetConfig = () => {
      config.value = { ...originalConfig.value }
    }

    // 重置到默认值
    const resetToDefaults = () => {
      config.value = { ...defaultMiddlewareSettings.config }
      originalConfig.value = { ...defaultMiddlewareSettings.originalConfig }
      connectionStatus.value = defaultMiddlewareSettings.connectionStatus
      middlewareVersion.value = defaultMiddlewareSettings.middlewareVersion
      isConnecting.value = defaultMiddlewareSettings.isConnecting
      lastConnectedAt.value = undefined
      lastError.value = undefined
    }

    // 导出配置
    const exportConfig = () => {
      return {
        config: config.value,
        originalConfig: originalConfig.value,
        connectionStatus: connectionStatus.value,
        middlewareVersion: middlewareVersion.value,
        lastConnectedAt: lastConnectedAt.value,
        exportTime: new Date().toISOString(),
      }
    }

    return {
      // 状态
      config,
      originalConfig,
      connectionStatus,
      middlewareVersion,
      isConnecting,
      lastConnectedAt,
      lastError,

      // 计算属性
      hasChanges,
      currentAddress,
      isConnected,

      // 方法
      updateConfig,
      setConnectionStatus,
      setMiddlewareVersion,
      setConnecting,
      loadConfig,
      testConnection,
      getVersion,
      saveConfig,
      restartApp,
      checkGrpcStatus,
      resetConfig,
      resetToDefaults,
      exportConfig,
    }
  },
  {
    persist: createPersistConfig('middleware'),
  }
)
