{"name": "@mattverse/electron-core", "version": "1.1.0", "private": true, "description": "Shared Electron core functionality for Mattverse applications", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js", "require": "./dist/types.cjs"}, "./handlers": {"types": "./dist/handlers.d.ts", "import": "./dist/handlers.js", "require": "./dist/handlers.cjs"}, "./preload": {"types": "./dist/preload.d.ts", "import": "./dist/preload.js", "require": "./dist/preload.cjs"}, "./grpc": {"import": "./dist/grpc.js", "types": "./dist/grpc.d.ts"}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@mattverse/shared": "workspace:*", "electron-log": "^5.2.0"}, "devDependencies": {"@mattverse/configs": "workspace:*", "electron": "^33.0.0", "electron-devtools-installer": "^3.2.0"}, "peerDependencies": {"electron": "^33.0.0", "@grpc/grpc-js": "^1.12.0", "@grpc/proto-loader": "^0.7.0", "protobufjs": "^7.4.0"}}