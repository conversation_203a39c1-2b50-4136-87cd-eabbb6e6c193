/**
 * Mattverse 应用相关 API
 */
import { ipc<PERSON>enderer } from 'electron'

/**
 * Mattverse 应用 API
 */
export const appAPI = {
  // Mattverse 特定配置
  getConfig: () => ipcRenderer.invoke('mattverse:get-config'),

  // Mattverse 特定功能
  mattverse: {
    getWorkflows: () => ipcRenderer.invoke('mattverse:get-workflows'),
    saveWorkflow: (workflow: any) => ipcRenderer.invoke('mattverse:save-workflow', workflow),
    deleteWorkflow: (id: string) => ipcRenderer.invoke('mattverse:delete-workflow', id)
  },
}
