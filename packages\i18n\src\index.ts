import { createI18n, type I18nOptions } from 'vue-i18n'
import type { App } from 'vue'

// 导入类型定义
import type { SupportedLocale, LocaleConfig } from './types'

// 导入语言包
import zhCN from './locales/zh-CN'
import enUS from './locales/en-US'

// 支持的语言列表
export const SUPPORTED_LOCALES: LocaleConfig[] = [
  { code: 'zh-CN', name: '简体中文', flag: '🇨🇳' },
  { code: 'en-US', name: 'English', flag: '🇺🇸' },
]

// 默认语言
export const DEFAULT_LOCALE: SupportedLocale = 'zh-CN'

// 语言包映射 - 使用 any 类型以兼容 vue-i18n
const messages: Record<SupportedLocale, any> = {
  'zh-CN': zhCN,
  'en-US': enUS,
}

// 创建 i18n 实例的工厂函数
export function createMattverseI18n(options: Partial<I18nOptions> = {}): ReturnType<typeof createI18n> {
  const defaultOptions: I18nOptions = {
    legacy: false, // 使用 Composition API 模式
    locale: DEFAULT_LOCALE,
    fallbackLocale: DEFAULT_LOCALE,
    messages,
    globalInjection: true, // 全局注入 $t, $tc 等
    silentTranslationWarn: true, // 生产环境下静默翻译警告
    silentFallbackWarn: true,
    ...options,
  }

  return createI18n(defaultOptions)
}

// Vue 插件安装函数
export function installI18n(app: App, options: Partial<I18nOptions> = {}): ReturnType<typeof createI18n> {
  const i18n = createMattverseI18n(options)
  app.use(i18n)
  return i18n
}

// 工具函数：获取浏览器语言
export function getBrowserLocale(): SupportedLocale {
  const browserLang = navigator.language || navigator.languages?.[0] || DEFAULT_LOCALE
  
  // 精确匹配
  if (SUPPORTED_LOCALES.some(locale => locale.code === browserLang)) {
    return browserLang as SupportedLocale
  }
  
  // 语言前缀匹配
  const langPrefix = browserLang.split('-')[0]
  const matchedLocale = SUPPORTED_LOCALES.find(locale => 
    locale.code.startsWith(langPrefix)
  )
  
  return matchedLocale?.code || DEFAULT_LOCALE
}

// 工具函数：设置文档语言属性
export function setDocumentLang(locale: SupportedLocale) {
  if (typeof document !== 'undefined') {
    document.documentElement.lang = locale
  }
}

// 导出类型和接口
export type { I18nOptions, SupportedLocale, LocaleConfig }
export { useI18n } from 'vue-i18n'
